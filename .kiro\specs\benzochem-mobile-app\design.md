# Design Document

## Overview

The Benzochem Industries mobile application will be built using React Native with Expo, providing a native mobile experience that mirrors the functionality and design language of the existing Next.js web application. The app will maintain the same vanilla latte theme system, user workflows, and business logic while optimizing for mobile interaction patterns and performance.

## Architecture

### Technology Stack

- **Framework**: React Native with Expo SDK 51+
- **Navigation**: React Navigation 6 with native stack and tab navigators
- **State Management**: React Context API with AsyncStorage persistence
- **UI Components**: Custom component library based on existing web design system
- **Styling**: StyleSheet with theme provider system
- **Storage**: AsyncStorage for local data persistence
- **Notifications**: Expo Notifications for push notifications
- **Authentication**: Local storage-based authentication system
- **Offline Support**: React Query with offline persistence

### Project Structure

```
benzochem-mobile/
├── src/
│   ├── components/           # Reusable UI components
│   │   ├── ui/              # Base UI components (Button, Input, Card, etc.)
│   │   ├── forms/           # Form components
│   │   ├── navigation/      # Navigation components
│   │   └── business/        # Business logic components
│   ├── screens/             # Screen components
│   │   ├── auth/           # Authentication screens
│   │   ├── products/       # Product catalog screens
│   │   ├── quotations/     # Quotation management screens
│   │   ├── account/        # User account screens
│   │   └── about/          # Company information screens
│   ├── contexts/           # React contexts
│   │   ├── AuthContext.tsx
│   │   ├── ThemeContext.tsx
│   │   └── QuotationContext.tsx
│   ├── services/           # API and data services
│   ├── utils/              # Utility functions
│   ├── constants/          # App constants and configuration
│   └── types/              # TypeScript type definitions
├── assets/                 # Static assets
└── app.json               # Expo configuration
```

## Components and Interfaces

### Core Components

#### 1. Theme System

```typescript
interface ThemeColors {
  primary: string;
  secondary: string;
  background: string;
  surface: string;
  text: string;
  textSecondary: string;
  border: string;
  accent: string;
  error: string;
  success: string;
  warning: string;
}

interface Theme {
  colors: ThemeColors;
  spacing: {
    xs: number;
    sm: number;
    md: number;
    lg: number;
    xl: number;
  };
  typography: {
    sizes: Record<string, number>;
    weights: Record<string, string>;
  };
  borderRadius: {
    sm: number;
    md: number;
    lg: number;
  };
}
```

**Vanilla Latte Theme Colors:**
- Light Mode: Warm vanilla backgrounds with teal accents
- Dark Mode: Deep vanilla-tinted dark backgrounds with bright teal accents
- Consistent with web application color palette

#### 2. Navigation Structure

```typescript
// Root Navigator
type RootStackParamList = {
  Auth: undefined;
  Main: undefined;
  ProductDetail: { productId: string };
  QuotationDetail: { quotationId: string };
};

// Main Tab Navigator
type MainTabParamList = {
  Home: undefined;
  Products: undefined;
  Quotations: undefined;
  Account: undefined;
  About: undefined;
};

// Products Stack
type ProductsStackParamList = {
  ProductList: undefined;
  ProductDetail: { productId: string };
  ProductSearch: undefined;
};
```

#### 3. Authentication Components

```typescript
interface User {
  id: string;
  email: string;
  name: string;
  phone?: string;
  businessInfo?: {
    companyName: string;
    gstNumber?: string;
    address: string;
    industry: string;
  };
  preferences: {
    theme: 'light' | 'dark';
    notifications: boolean;
  };
}

interface AuthContextType {
  user: User | null;
  isLoading: boolean;
  login: (email: string, password: string) => Promise<void>;
  register: (userData: RegisterData) => Promise<void>;
  logout: () => Promise<void>;
  updateProfile: (updates: Partial<User>) => Promise<void>;
}
```

#### 4. Product Components

```typescript
interface Product {
  id: string;
  name: string;
  category: string;
  description: string;
  specifications: {
    purity: string;
    grade: string;
    cas: string;
    molecularFormula: string;
    molecularWeight: string;
  };
  pricing: {
    basePrice: number;
    currency: string;
    unit: string;
    minimumOrder: number;
  };
  safety: {
    hazardClass: string;
    storageConditions: string;
    handlingInstructions: string;
  };
  images: string[];
  availability: 'in-stock' | 'limited' | 'out-of-stock';
}

interface ProductCardProps {
  product: Product;
  onPress: (product: Product) => void;
  onQuoteRequest: (product: Product) => void;
}
```

#### 5. Quotation System

```typescript
interface QuotationItem {
  productId: string;
  product: Product;
  quantity: number;
  unit: string;
  notes?: string;
}

interface Quotation {
  id: string;
  userId: string;
  items: QuotationItem[];
  status: 'draft' | 'submitted' | 'processing' | 'completed';
  createdAt: Date;
  updatedAt: Date;
  totalItems: number;
  notes?: string;
}

interface QuotationContextType {
  currentQuotation: Quotation | null;
  quotationHistory: Quotation[];
  addToQuotation: (product: Product, quantity: number) => void;
  removeFromQuotation: (productId: string) => void;
  updateQuantity: (productId: string, quantity: number) => void;
  submitQuotation: () => Promise<void>;
  clearQuotation: () => void;
}
```

### Screen Components

#### 1. Authentication Screens

- **SplashScreen**: Benzochem branding with loading animation
- **LoginScreen**: Email/password login with biometric option
- **RegisterScreen**: Multi-step registration with business info
- **ForgotPasswordScreen**: Password recovery flow

#### 2. Main Application Screens

- **HomeScreen**: Dashboard with quick actions and statistics
- **ProductListScreen**: Searchable product catalog with filters
- **ProductDetailScreen**: Detailed product information and specifications
- **QuotationScreen**: Current quotation management
- **QuotationHistoryScreen**: Past quotations with status tracking
- **AccountScreen**: User profile and settings management
- **AboutScreen**: Company timeline and leadership information

#### 3. Navigation Components

```typescript
interface TabBarProps {
  state: NavigationState;
  descriptors: Record<string, Descriptor>;
  navigation: NavigationHelpers;
}

interface HeaderProps {
  title: string;
  showBack?: boolean;
  rightAction?: React.ReactNode;
  theme: Theme;
}
```

## Data Models

### Local Storage Schema

```typescript
// AsyncStorage Keys
const STORAGE_KEYS = {
  USER_DATA: '@benzochem:user',
  QUOTATIONS: '@benzochem:quotations',
  PRODUCTS_CACHE: '@benzochem:products',
  THEME_PREFERENCE: '@benzochem:theme',
  SETTINGS: '@benzochem:settings',
} as const;

// Storage Service Interface
interface StorageService {
  getUser(): Promise<User | null>;
  setUser(user: User): Promise<void>;
  getQuotations(): Promise<Quotation[]>;
  setQuotations(quotations: Quotation[]): Promise<void>;
  getTheme(): Promise<'light' | 'dark'>;
  setTheme(theme: 'light' | 'dark'): Promise<void>;
  clearAll(): Promise<void>;
}
```

### Product Data Structure

```typescript
interface ProductCategory {
  id: string;
  name: string;
  description: string;
  icon: string;
  productCount: number;
}

interface ProductFilter {
  category?: string;
  availability?: string;
  priceRange?: {
    min: number;
    max: number;
  };
  searchQuery?: string;
}
```

## Error Handling

### Error Types

```typescript
enum ErrorType {
  NETWORK_ERROR = 'NETWORK_ERROR',
  VALIDATION_ERROR = 'VALIDATION_ERROR',
  AUTHENTICATION_ERROR = 'AUTHENTICATION_ERROR',
  STORAGE_ERROR = 'STORAGE_ERROR',
  PERMISSION_ERROR = 'PERMISSION_ERROR',
}

interface AppError {
  type: ErrorType;
  message: string;
  details?: any;
  timestamp: Date;
}

interface ErrorBoundaryState {
  hasError: boolean;
  error: AppError | null;
}
```

### Error Handling Strategy

1. **Global Error Boundary**: Catch and display unhandled errors
2. **Network Error Handling**: Retry logic with exponential backoff
3. **Validation Errors**: Real-time form validation with clear messaging
4. **Storage Errors**: Fallback to default values with user notification
5. **Permission Errors**: Clear instructions for enabling required permissions

## Testing Strategy

### Testing Approach

1. **Unit Tests**: Jest for utility functions and business logic
2. **Component Tests**: React Native Testing Library for component behavior
3. **Integration Tests**: Test complete user workflows
4. **E2E Tests**: Detox for critical user journeys
5. **Performance Tests**: Monitor app performance and memory usage

### Test Coverage Areas

- Authentication flows (login, register, logout)
- Product browsing and search functionality
- Quotation management (add, remove, submit)
- Theme switching and persistence
- Offline functionality
- Push notification handling

### Testing Configuration

```typescript
// Jest Configuration
export default {
  preset: 'react-native',
  setupFilesAfterEnv: ['<rootDir>/src/test/setup.ts'],
  testMatch: ['**/__tests__/**/*.test.{js,ts,tsx}'],
  collectCoverageFrom: [
    'src/**/*.{js,ts,tsx}',
    '!src/**/*.d.ts',
    '!src/test/**',
  ],
  coverageThreshold: {
    global: {
      branches: 80,
      functions: 80,
      lines: 80,
      statements: 80,
    },
  },
};
```

## Performance Considerations

### Optimization Strategies

1. **Image Optimization**: Use Expo Image with caching and lazy loading
2. **List Performance**: FlatList with getItemLayout and keyExtractor
3. **Memory Management**: Proper cleanup of listeners and subscriptions
4. **Bundle Size**: Code splitting and dynamic imports where possible
5. **Startup Performance**: Lazy load non-critical screens and components

### Performance Monitoring

```typescript
interface PerformanceMetrics {
  appStartTime: number;
  screenTransitionTime: number;
  apiResponseTime: number;
  memoryUsage: number;
  crashRate: number;
}

interface PerformanceService {
  trackScreenView(screenName: string): void;
  trackUserAction(action: string, properties?: Record<string, any>): void;
  trackError(error: Error, context?: string): void;
  measurePerformance(operation: string, duration: number): void;
}
```

## Security Considerations

### Data Security

1. **Local Storage Encryption**: Sensitive data encrypted using Expo SecureStore
2. **Biometric Authentication**: Secure biometric authentication for login
3. **API Security**: Secure API communication with proper headers
4. **Input Validation**: Comprehensive input validation and sanitization
5. **Session Management**: Secure session handling with automatic logout

### Privacy Compliance

1. **Data Minimization**: Only collect necessary user data
2. **Consent Management**: Clear consent for data collection and notifications
3. **Data Retention**: Implement data retention policies
4. **User Rights**: Provide data export and deletion capabilities

## Accessibility Features

### Accessibility Implementation

1. **Screen Reader Support**: Proper accessibility labels and hints
2. **Keyboard Navigation**: Full keyboard navigation support
3. **Color Contrast**: WCAG compliant color combinations
4. **Font Scaling**: Support for system font size preferences
5. **Voice Control**: Voice control compatibility

```typescript
interface AccessibilityProps {
  accessibilityLabel?: string;
  accessibilityHint?: string;
  accessibilityRole?: string;
  accessibilityState?: {
    disabled?: boolean;
    selected?: boolean;
    checked?: boolean;
  };
}
```

## Platform-Specific Considerations

### iOS Specific Features

1. **iOS Design Guidelines**: Follow iOS Human Interface Guidelines
2. **Safe Area Handling**: Proper safe area insets for notched devices
3. **iOS Permissions**: Handle iOS-specific permission requests
4. **App Store Guidelines**: Ensure compliance with App Store review guidelines

### Android Specific Features

1. **Material Design**: Implement Material Design principles
2. **Android Permissions**: Handle Android permission system
3. **Back Button Handling**: Proper Android back button behavior
4. **Play Store Guidelines**: Ensure compliance with Play Store policies

## Deployment Strategy

### Build Configuration

```typescript
// app.json configuration
{
  "expo": {
    "name": "Benzochem Industries",
    "slug": "benzochem-mobile",
    "version": "1.0.0",
    "orientation": "portrait",
    "icon": "./assets/icon.png",
    "userInterfaceStyle": "automatic",
    "splash": {
      "image": "./assets/splash.png",
      "resizeMode": "contain",
      "backgroundColor": "#f8f6f0"
    },
    "assetBundlePatterns": ["**/*"],
    "ios": {
      "supportsTablet": true,
      "bundleIdentifier": "com.benzochem.mobile"
    },
    "android": {
      "adaptiveIcon": {
        "foregroundImage": "./assets/adaptive-icon.png",
        "backgroundColor": "#f8f6f0"
      },
      "package": "com.benzochem.mobile"
    },
    "plugins": [
      "expo-notifications",
      "expo-secure-store",
      "expo-local-authentication"
    ]
  }
}
```

### Release Process

1. **Development Build**: EAS Build for internal testing
2. **Staging Environment**: TestFlight/Internal Testing for QA
3. **Production Release**: App Store/Play Store submission
4. **OTA Updates**: Expo Updates for non-native changes