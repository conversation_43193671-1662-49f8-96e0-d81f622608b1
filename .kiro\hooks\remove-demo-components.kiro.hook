{"enabled": true, "name": "Remove Demo Components", "description": "Automatically detects and removes demo components, pages, and mock data while integrating real-time API connections", "version": "1", "when": {"type": "fileEdited", "patterns": ["user/components/**/*.tsx", "user/components/**/*.ts", "user/app/**/*.tsx", "user/app/**/*.ts", "user/pages/**/*.tsx", "user/pages/**/*.ts"]}, "then": {"type": "askAgent", "prompt": "Analyze the modified files and identify any demo components, mock data, or placeholder content. Remove these demo elements and replace them with real-time API integrations. Ensure all components now fetch data from actual API endpoints rather than using hardcoded demo data. Update imports, remove unused demo files, and establish proper API connections."}}