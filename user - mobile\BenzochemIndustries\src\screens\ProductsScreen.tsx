import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  SafeAreaView,
  StatusBar,
  RefreshControl,
  Modal,
  ScrollView,
} from 'react-native';
import { 
  Filter, 
  Grid3X3, 
  List, 
  Search,
  X,
  Package,
  ArrowLeft,
  SlidersHorizontal,
  TrendingUp,
  Star
} from 'lucide-react-native';
import { useNavigation, useRoute } from '@react-navigation/native';

import { useTheme } from '../contexts/ThemeContext';
import { useQuotation } from '../contexts/QuotationContext';
import { apiClient } from '../services/api';
import { Product } from '../types';
import { AppNavigationProp } from '../types/navigation';
import ProductCard from '../components/ProductCard';
import LoadingSpinner from '../components/LoadingSpinner';

const ProductsScreen = () => {
  const { theme } = useTheme();
  const { addItem } = useQuotation();
  const navigation = useNavigation<AppNavigationProp>();
  const route = useRoute();
  const { category } = route.params as { category?: string } || {};

  const [products, setProducts] = useState<Product[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [filterModalVisible, setFilterModalVisible] = useState(false);
  const [selectedCategory, setSelectedCategory] = useState(category || 'all');
  const [sortBy, setSortBy] = useState('name');

  useEffect(() => {
    loadProducts();
  }, [selectedCategory, sortBy]);

  const loadProducts = async () => {
    try {
      const response = await apiClient.getProducts({
        category: selectedCategory === 'all' ? undefined : selectedCategory,
        sortBy,
      });
      if (response.success && response.data) {
        const transformedProducts = Array.isArray(response.data) 
          ? response.data.map(product => apiClient.transformApiProductToProduct(product))
          : [];
        setProducts(transformedProducts);
      } else {
        setProducts([]);
      }
    } catch (error) {
      console.error('Error loading products:', error);
      setProducts([]);
    } finally {
      setLoading(false);
    }
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await loadProducts();
    setRefreshing(false);
  };

  const handleAddToQuotation = (product: Product) => {
    addItem({
      productId: product.id,
      productName: product.title,
      quantity: '1',
      unit: 'kg',
      specifications: '',
    });
  };

  const renderGridProduct = ({ item }: { item: Product }) => (
    <View style={styles.gridProductContainer}>
      <ProductCard product={item} variant="compact" />
    </View>
  );

  const renderListProduct = ({ item }: { item: Product }) => (
    <TouchableOpacity
      style={[styles.listProductCard, { backgroundColor: theme.colors.card }]}
      onPress={() => navigation.navigate('ProductDetail', { productId: item.id })}
    >
      <View style={[styles.listProductImage, { backgroundColor: theme.colors.surface }]}>
        <Package size={24} color={theme.colors.textSecondary} />
      </View>
      
      <View style={styles.listProductInfo}>
        <Text style={[styles.listProductTitle, { color: theme.colors.text }]} numberOfLines={2}>
          {item.title}
        </Text>
        <Text style={[styles.listProductCategory, { color: theme.colors.textSecondary }]}>
          Chemical Product
        </Text>
        <Text style={[styles.listProductPrice, { color: theme.colors.primary }]}>
          Request Quote
        </Text>
      </View>
      
      <TouchableOpacity
        style={[styles.listAddButton, { backgroundColor: theme.colors.primary }]}
        onPress={() => handleAddToQuotation(item)}
      >
        <Text style={[styles.listAddButtonText, { color: theme.colors.background }]}>
          Quote
        </Text>
      </TouchableOpacity>
    </TouchableOpacity>
  );

  const FilterModal = () => (
    <Modal
      visible={filterModalVisible}
      animationType="slide"
      presentationStyle="pageSheet"
      onRequestClose={() => setFilterModalVisible(false)}
    >
      <View style={[styles.modalContainer, { backgroundColor: theme.colors.background }]}>
        <SafeAreaView style={styles.modalContent}>
          {/* Modal Header */}
          <View style={styles.modalHeader}>
            <Text style={[styles.modalTitle, { color: theme.colors.text }]}>
              Filters & Sort
            </Text>
            <TouchableOpacity onPress={() => setFilterModalVisible(false)}>
              <X size={24} color={theme.colors.text} />
            </TouchableOpacity>
          </View>

          <ScrollView style={styles.modalBody}>
            {/* Categories */}
            <View style={styles.filterSection}>
              <Text style={[styles.filterSectionTitle, { color: theme.colors.text }]}>
                Category
              </Text>
              {[
                { key: 'all', label: 'All Products', icon: Package },
                { key: 'powder', label: 'Powder Products', icon: Package },
                { key: 'liquid', label: 'Liquid Products', icon: Package },
                { key: 'trending', label: 'Trending', icon: TrendingUp },
                { key: 'new', label: 'New Arrivals', icon: Star },
              ].map((cat) => (
                <TouchableOpacity
                  key={cat.key}
                  style={[
                    styles.filterOption,
                    { backgroundColor: selectedCategory === cat.key ? theme.colors.primary + '20' : 'transparent' }
                  ]}
                  onPress={() => setSelectedCategory(cat.key)}
                >
                  <cat.icon size={20} color={selectedCategory === cat.key ? theme.colors.primary : theme.colors.textSecondary} />
                  <Text style={[
                    styles.filterOptionText,
                    { color: selectedCategory === cat.key ? theme.colors.primary : theme.colors.text }
                  ]}>
                    {cat.label}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>

            {/* Sort By */}
            <View style={styles.filterSection}>
              <Text style={[styles.filterSectionTitle, { color: theme.colors.text }]}>
                Sort By
              </Text>
              {[
                { key: 'name', label: 'Name (A-Z)' },
                { key: 'name_desc', label: 'Name (Z-A)' },
                { key: 'newest', label: 'Newest First' },
                { key: 'popular', label: 'Most Popular' },
                { key: 'price_low', label: 'Price: Low to High' },
                { key: 'price_high', label: 'Price: High to Low' },
              ].map((option) => (
                <TouchableOpacity
                  key={option.key}
                  style={[
                    styles.filterOption,
                    { backgroundColor: sortBy === option.key ? theme.colors.primary + '20' : 'transparent' }
                  ]}
                  onPress={() => setSortBy(option.key)}
                >
                  <Text style={[
                    styles.filterOptionText,
                    { color: sortBy === option.key ? theme.colors.primary : theme.colors.text }
                  ]}>
                    {option.label}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>
          </ScrollView>

          {/* Apply Button */}
          <View style={styles.modalFooter}>
            <TouchableOpacity
              style={[styles.applyButton, { backgroundColor: theme.colors.primary }]}
              onPress={() => {
                setFilterModalVisible(false);
                loadProducts();
              }}
            >
              <Text style={[styles.applyButtonText, { color: theme.colors.background }]}>
                Apply Filters
              </Text>
            </TouchableOpacity>
          </View>
        </SafeAreaView>
      </View>
    </Modal>
  );

  if (loading) {
    return <LoadingSpinner />;
  }

  return (
    <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
      <StatusBar 
        barStyle={theme.dark ? 'light-content' : 'dark-content'} 
        backgroundColor="transparent"
        translucent
      />
      
      <View style={styles.topPadding} />
      {/* Header */}
      <View style={[styles.header, { backgroundColor: theme.colors.background }]}>
        <View style={styles.headerContent}>
          <TouchableOpacity
            style={styles.backButton}
            onPress={() => navigation.goBack()}
          >
            <ArrowLeft size={24} color={theme.colors.text} />
          </TouchableOpacity>
          
          <View style={styles.headerCenter}>
            <Text style={[styles.headerTitle, { color: theme.colors.text }]}>
              Products
            </Text>
            <Text style={[styles.headerSubtitle, { color: theme.colors.textSecondary }]}>
              {products.length} items
            </Text>
          </View>
          
          <View style={styles.headerActions}>
            <TouchableOpacity
              style={[styles.headerButton, { backgroundColor: theme.colors.surface }]}
              onPress={() => navigation.navigate('Search')}
            >
              <Search size={20} color={theme.colors.text} />
            </TouchableOpacity>
            
            <TouchableOpacity
              style={[styles.headerButton, { backgroundColor: theme.colors.surface }]}
              onPress={() => setFilterModalVisible(true)}
            >
              <SlidersHorizontal size={20} color={theme.colors.text} />
            </TouchableOpacity>
          </View>
        </View>

        {/* Filter Bar */}
        <View style={styles.filterBar}>
          <ScrollView 
            horizontal 
            showsHorizontalScrollIndicator={false}
            contentContainerStyle={styles.filterTags}
          >
            {[
              { key: 'all', label: 'All' },
              { key: 'powder', label: 'Powder' },
              { key: 'liquid', label: 'Liquid' },
              { key: 'trending', label: 'Trending' },
              { key: 'new', label: 'New' },
            ].map((filter) => (
              <TouchableOpacity
                key={filter.key}
                style={[
                  styles.filterTag,
                  { 
                    backgroundColor: selectedCategory === filter.key ? theme.colors.primary : theme.colors.surface,
                  }
                ]}
                onPress={() => setSelectedCategory(filter.key)}
              >
                <Text style={[
                  styles.filterTagText,
                  { color: selectedCategory === filter.key ? theme.colors.background : theme.colors.text }
                ]}>
                  {filter.label}
                </Text>
              </TouchableOpacity>
            ))}
          </ScrollView>
          
          <TouchableOpacity
            style={[styles.viewModeButton, { backgroundColor: theme.colors.surface }]}
            onPress={() => setViewMode(viewMode === 'grid' ? 'list' : 'grid')}
          >
            {viewMode === 'grid' ? (
              <List size={18} color={theme.colors.text} />
            ) : (
              <Grid3X3 size={18} color={theme.colors.text} />
            )}
          </TouchableOpacity>
        </View>
      </View>

      {/* Products List */}
      <FlatList
        data={products}
        renderItem={viewMode === 'grid' ? renderGridProduct : renderListProduct}
        keyExtractor={(item) => item.id}
        numColumns={viewMode === 'grid' ? 2 : 1}
        key={viewMode} // Force re-render when view mode changes
        contentContainerStyle={styles.productsList}
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl 
            refreshing={refreshing} 
            onRefresh={onRefresh}
            tintColor={theme.colors.primary}
            colors={[theme.colors.primary]}
          />
        }
        ListEmptyComponent={() => (
          <View style={styles.emptyState}>
            <Package size={64} color={theme.colors.textSecondary} />
            <Text style={[styles.emptyStateTitle, { color: theme.colors.text }]}>
              No products found
            </Text>
            <Text style={[styles.emptyStateDescription, { color: theme.colors.textSecondary }]}>
              Try adjusting your filters or search terms
            </Text>
          </View>
        )}
      />

      <FilterModal />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  topPadding: {
    height: 30,
  },
  
  // Header
  header: {
    paddingHorizontal: 20,
    paddingTop: 16,
    paddingBottom: 12,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  backButton: {
    width: 44,
    height: 44,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  headerCenter: {
    flex: 1,
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: '900',
    letterSpacing: -0.5,
  },
  headerSubtitle: {
    fontSize: 14,
    fontWeight: '500',
    marginTop: 2,
  },
  headerActions: {
    flexDirection: 'row',
    gap: 8,
  },
  headerButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },

  // Filter Bar
  filterBar: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  filterTags: {
    paddingRight: 12,
  },
  filterTag: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    marginRight: 8,
  },
  filterTagText: {
    fontSize: 14,
    fontWeight: '600',
  },
  viewModeButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    justifyContent: 'center',
    alignItems: 'center',
  },

  // Products List
  productsList: {
    padding: 16,
  },

  // Grid View
  gridProductContainer: {
    flex: 1,
    paddingHorizontal: 4,
  },

  // List View
  listProductCard: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    marginVertical: 6,
    borderRadius: 16,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  listProductImage: {
    width: 60,
    height: 60,
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  listProductInfo: {
    flex: 1,
  },
  listProductTitle: {
    fontSize: 16,
    fontWeight: '700',
    marginBottom: 4,
  },
  listProductCategory: {
    fontSize: 14,
    fontWeight: '500',
    marginBottom: 4,
  },
  listProductPrice: {
    fontSize: 14,
    fontWeight: '700',
  },
  listAddButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 16,
  },
  listAddButtonText: {
    fontSize: 12,
    fontWeight: '700',
  },

  // Empty State
  emptyState: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 60,
  },
  emptyStateTitle: {
    fontSize: 20,
    fontWeight: '700',
    marginTop: 16,
    marginBottom: 8,
  },
  emptyStateDescription: {
    fontSize: 16,
    textAlign: 'center',
  },

  // Filter Modal
  modalContainer: {
    flex: 1,
  },
  modalContent: {
    flex: 1,
    padding: 20,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 24,
  },
  modalTitle: {
    fontSize: 24,
    fontWeight: '900',
    letterSpacing: -0.5,
  },
  modalBody: {
    flex: 1,
  },
  filterSection: {
    marginBottom: 32,
  },
  filterSectionTitle: {
    fontSize: 18,
    fontWeight: '700',
    marginBottom: 16,
  },
  filterOption: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 12,
    marginBottom: 8,
    gap: 12,
  },
  filterOptionText: {
    fontSize: 16,
    fontWeight: '600',
  },
  modalFooter: {
    paddingTop: 20,
  },
  applyButton: {
    paddingVertical: 16,
    borderRadius: 12,
    alignItems: 'center',
  },
  applyButtonText: {
    fontSize: 16,
    fontWeight: '700',
  },
});

export default ProductsScreen;