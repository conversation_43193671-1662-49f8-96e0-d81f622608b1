# Benzochem Admin - Simplified Login-Only Authentication

This document explains the simplified authentication system for the Benzochem Admin dashboard, which uses a login-only approach with pre-existing admin credentials stored in the Convex database.

## 🎯 Overview

The authentication system has been simplified to remove the setup flow complexity. Instead of allowing users to create initial admin accounts through a setup page, the system now:

1. **Uses pre-existing admin credentials** stored in the Convex database
2. **Provides only a login page** as the entry point for authentication
3. **Redirects all unauthenticated users** directly to `/login`
4. **Eliminates setup-related complexity** and potential security issues

## 🔑 Default Admin Credentials

The system comes with a default admin account:

```
Email: <EMAIL>
Password: admin123
Role: Super Admin
```

**⚠️ Important**: Change these credentials in production!

## 🚀 Getting Started

### 1. Start the Development Servers

```bash
# Terminal 1: Start Convex
npx convex dev

# Terminal 2: Start Next.js
npm run dev
```

### 2. Ensure Admin Account Exists

If you need to create the default admin account, you can use the Convex dashboard or call the mutation:

```javascript
// In Convex dashboard, run this mutation:
seed.ensureAdminExists()
```

This will either:
- Create the default admin account if none exist
- Return information about existing admin accounts

### 3. Login to Admin Dashboard

1. Navigate to `http://localhost:3000`
2. You'll be redirected to `/login`
3. Use the default credentials:
   - **Email**: `<EMAIL>`
   - **Password**: `admin123`
4. You'll be redirected to the admin dashboard

## 🏗️ System Architecture

### **Simplified Flow**
```
User visits any URL → Middleware checks authentication → 
If not authenticated: Redirect to /login → 
User logs in with existing credentials → 
Redirect to dashboard
```

### **Removed Components**
- ❌ `/setup` page and route
- ❌ `SetupCheck` component
- ❌ Setup API route (`/api/auth/setup`)
- ❌ `setupInitialAdmin` mutation
- ❌ Admin existence checks in middleware
- ❌ Setup-related redirects

### **Simplified Components**
- ✅ `/login` page only
- ✅ Direct authentication with existing credentials
- ✅ Simplified middleware routing
- ✅ Secure cookie-based sessions

## 📁 File Structure

```
admin/
├── src/
│   ├── app/
│   │   ├── login/page.tsx          # Login page (simplified)
│   │   └── dashboard/              # Protected dashboard routes
│   ├── contexts/
│   │   └── auth-context.tsx        # Authentication context
│   └── middleware.ts               # Simplified routing middleware
├── convex/
│   ├── auth.ts                     # Authentication functions
│   ├── admins.ts                   # Admin management
│   └── seed.ts                     # Database seeding with admin creation
└── LOGIN_ONLY_AUTHENTICATION.md   # This file
```

## 🔧 Creating Additional Admin Accounts

### Method 1: Using Convex Dashboard

1. Open the Convex dashboard
2. Navigate to the `admins` table
3. Insert a new record with:
   ```json
   {
     "email": "<EMAIL>",
     "passwordHash": "[hashed_password]",
     "firstName": "New",
     "lastName": "Admin",
     "role": "admin",
     "permissions": ["users.read", "products.read", ...],
     "isActive": true,
     "createdAt": [timestamp],
     "updatedAt": [timestamp]
   }
   ```

### Method 2: Using createAdmin Mutation

```javascript
// In Convex dashboard, call:
admins.createAdmin({
  email: "<EMAIL>",
  password: "securepassword123",
  firstName: "New",
  lastName: "Admin",
  role: "admin",
  permissions: ["users.read", "products.read", "settings.read"]
})
```

### Method 3: Using the Admin Creation Script

```bash
node scripts/create-admin.js
```

Follow the prompts to generate admin account details.

## 🛡️ Security Features

### **Maintained Security**
- ✅ **httpOnly Cookies**: Session tokens stored in secure cookies
- ✅ **CSRF Protection**: Cross-site request forgery prevention
- ✅ **Password Hashing**: All passwords are hashed before storage
- ✅ **Session Validation**: Real-time session validation with Convex
- ✅ **Route Protection**: Middleware protects all admin routes

### **Enhanced Security**
- ✅ **No Setup Vulnerabilities**: Eliminates setup-related attack vectors
- ✅ **Simplified Attack Surface**: Fewer entry points for potential attacks
- ✅ **Controlled Admin Creation**: Admin accounts created through secure methods only

## 🔄 Migration from Setup System

If you were previously using the setup system:

### 1. Clear Browser Data
```javascript
// In browser console:
localStorage.clear();
// Or specifically:
localStorage.removeItem("benzochem_admin_id");
localStorage.removeItem("benzochem_admin_data");
```

### 2. Ensure Admin Exists
Run the `ensureAdminExists` mutation to create the default admin if needed.

### 3. Login with Credentials
Use the default credentials or any existing admin account credentials.

## 🧪 Testing the System

### **Basic Login Test**
1. Navigate to `http://localhost:3000`
2. Should redirect to `/login`
3. Enter credentials: `<EMAIL>` / `admin123`
4. Should redirect to `/dashboard`
5. Verify you can access admin features

### **Route Protection Test**
1. Logout from the admin dashboard
2. Try to access `http://localhost:3000/dashboard` directly
3. Should redirect to `/login`
4. Login and verify you're redirected back to dashboard

### **Session Persistence Test**
1. Login to the admin dashboard
2. Refresh the page
3. Should remain logged in
4. Close and reopen browser
5. Should remain logged in (until session expires)

## 🚨 Production Considerations

### **1. Change Default Credentials**
```javascript
// Create a new admin with secure credentials
admins.createAdmin({
  email: "<EMAIL>",
  password: "very-secure-password-123!",
  firstName: "Your",
  lastName: "Name",
  role: "super_admin",
  permissions: [/* all permissions */]
})

// Then deactivate or delete the default admin
admins.updateAdminStatus({
  adminId: "default-admin-id",
  isActive: false
})
```

### **2. Environment Variables**
Ensure these are set in production:
```bash
JWT_SECRET=your-production-jwt-secret-at-least-32-characters
SESSION_SECRET=your-production-session-secret-at-least-32-characters
```

### **3. Database Security**
- Use strong passwords for all admin accounts
- Regularly audit admin account access
- Implement account lockout policies
- Monitor login attempts and failures

## 📞 Troubleshooting

### **"Invalid email or password" Error**
- Verify the admin account exists in the Convex database
- Check that the password is correct (`admin123` for default account)
- Ensure the admin account is active (`isActive: true`)

### **Redirect Loop Issues**
- Clear browser localStorage and cookies
- Verify middleware configuration
- Check that admin account exists in database

### **Session Not Persisting**
- Check that cookies are being set correctly
- Verify JWT_SECRET and SESSION_SECRET are configured
- Ensure HTTPS is used in production

### **Cannot Access Admin Routes**
- Verify you're logged in with valid credentials
- Check that the admin account has proper permissions
- Ensure middleware is protecting routes correctly

## 🎉 Benefits of Simplified System

1. **Reduced Complexity**: No setup flow to manage or secure
2. **Better Security**: Fewer attack vectors and entry points
3. **Easier Deployment**: No initial setup required
4. **Consistent Experience**: Always starts with login
5. **Simplified Maintenance**: Less code to maintain and debug

The login-only authentication system provides a secure, simple, and reliable way to access the Benzochem Admin dashboard using pre-existing credentials stored in the database.
