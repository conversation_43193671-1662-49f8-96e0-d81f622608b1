[{"level_": 0, "message_": "Start JSON generation. Platform version: 24 min SDK version: x86", "file_": "D:\\edit-1\\user - mobile\\BenzochemIndustries\\node_modules\\react-native-screens\\android\\CMakeLists.txt", "tag_": "debug|x86", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "JSON 'D:\\edit-1\\user - mobile\\BenzochemIndustries\\node_modules\\react-native-screens\\android\\.cxx\\Debug\\451t4q48\\x86\\android_gradle_build.json' was up-to-date", "file_": "D:\\edit-1\\user - mobile\\BenzochemIndustries\\node_modules\\react-native-screens\\android\\CMakeLists.txt", "tag_": "debug|x86", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "JSON generation completed without problems", "file_": "D:\\edit-1\\user - mobile\\BenzochemIndustries\\node_modules\\react-native-screens\\android\\CMakeLists.txt", "tag_": "debug|x86", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}]