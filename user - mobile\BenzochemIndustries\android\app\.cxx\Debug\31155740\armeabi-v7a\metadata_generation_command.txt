                        -HD:\edit-1\user - mobile\BenzochemIndustries\node_modules\react-native\ReactAndroid\cmake-utils\default-app-setup
-DCMAKE_SYSTEM_NAME=Android
-DCMAKE_EXPORT_COMPILE_COMMANDS=ON
-DCMAKE_SYSTEM_VERSION=24
-DANDROID_PLATFORM=android-24
-DANDROID_ABI=armeabi-v7a
-DCMAKE_ANDROID_ARCH_ABI=armeabi-v7a
-DANDROID_NDK=C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\27.1.12297006
-DCMAKE_ANDROID_NDK=C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\27.1.12297006
-DCMAKE_TOOLCHAIN_FILE=C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\27.1.12297006\build\cmake\android.toolchain.cmake
-DCMAKE_MAKE_PROGRAM=C:\Users\<USER>\AppData\Local\Android\Sdk\cmake\3.22.1\bin\ninja.exe
-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=D:\edit-1\user - mobile\BenzochemIndustries\android\app\build\intermediates\cxx\Debug\31155740\obj\armeabi-v7a
-DCMAKE_RUNTIME_OUTPUT_DIRECTORY=D:\edit-1\user - mobile\BenzochemIndustries\android\app\build\intermediates\cxx\Debug\31155740\obj\armeabi-v7a
-DCMAKE_BUILD_TYPE=Debug
-DCMAKE_FIND_ROOT_PATH=D:\edit-1\user - mobile\BenzochemIndustries\android\app\.cxx\Debug\31155740\prefab\armeabi-v7a\prefab
-BD:\edit-1\user - mobile\BenzochemIndustries\android\app\.cxx\Debug\31155740\armeabi-v7a
-GNinja
-DPROJECT_BUILD_DIR=D:\edit-1\user - mobile\BenzochemIndustries\android\app\build
-DPROJECT_ROOT_DIR=D:\edit-1\user - mobile\BenzochemIndustries\android
-DREACT_ANDROID_DIR=D:\edit-1\user - mobile\BenzochemIndustries\node_modules\react-native\ReactAndroid
-DANDROID_STL=c++_shared
-DANDROID_SUPPORT_FLEXIBLE_PAGE_SIZES=ON
                        Build command args: []
                        Version: 2