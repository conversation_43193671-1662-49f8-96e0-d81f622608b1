{"artifacts": [{"path": "C:/BenzochemApp/android/app/build/intermediates/cxx/Debug/5d6b2m5p/obj/arm64-v8a/libappmodules.so"}], "backtrace": 3, "backtraceGraph": {"commands": ["add_library", "include", "target_link_libraries", "target_compile_options", "target_compile_reactnative_options", "target_include_directories"], "files": ["C:/BenzochemApp/node_modules/react-native/ReactAndroid/cmake-utils/ReactNative-application.cmake", "CMakeLists.txt", "C:/BenzochemApp/node_modules/react-native/ReactCommon/cmake-utils/react-native-flags.cmake", "D:/Benzochem Industries - Mobile/edit-1/user - mobile/BenzochemIndustries/node_modules/react-native-safe-area-context/android/src/main/jni/CMakeLists.txt", "D:/Benzochem Industries - Mobile/edit-1/user - mobile/BenzochemIndustries/node_modules/react-native-screens/android/src/main/jni/CMakeLists.txt", "D:/Benzochem Industries - Mobile/edit-1/user - mobile/BenzochemIndustries/node_modules/react-native-svg/android/src/main/jni/CMakeLists.txt"], "nodes": [{"file": 1}, {"command": 1, "file": 1, "line": 31, "parent": 0}, {"file": 0, "parent": 1}, {"command": 0, "file": 0, "line": 64, "parent": 2}, {"command": 2, "file": 0, "line": 95, "parent": 2}, {"command": 2, "file": 0, "line": 81, "parent": 2}, {"command": 4, "file": 0, "line": 71, "parent": 2}, {"command": 3, "file": 2, "line": 30, "parent": 6}, {"command": 3, "file": 2, "line": 36, "parent": 6}, {"command": 5, "file": 0, "line": 66, "parent": 2}, {"file": 3}, {"command": 5, "file": 3, "line": 83, "parent": 10}, {"file": 4}, {"command": 5, "file": 4, "line": 81, "parent": 12}, {"file": 5}, {"command": 5, "file": 5, "line": 89, "parent": 14}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "-g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC"}, {"backtrace": 7, "fragment": "-Wall"}, {"backtrace": 7, "fragment": "-Werror"}, {"backtrace": 7, "fragment": "-fexceptions"}, {"backtrace": 7, "fragment": "-frtti"}, {"backtrace": 7, "fragment": "-std=c++20"}, {"backtrace": 7, "fragment": "-DFOLLY_NO_CONFIG=1"}, {"backtrace": 8, "fragment": "-DLOG_TAG=\\\"ReactNative\\\""}, {"backtrace": 4, "fragment": "-DFOLLY_HAVE_CLOCK_GETTIME=1"}, {"backtrace": 4, "fragment": "-DFOLLY_USE_LIBCPP=1"}, {"backtrace": 4, "fragment": "-DFOLLY_CFG_NO_COROUTINES=1"}, {"backtrace": 4, "fragment": "-DFOLLY_MOBILE=1"}, {"backtrace": 4, "fragment": "-DFOLLY_HAVE_RECVMMSG=1"}, {"backtrace": 4, "fragment": "-DFOLLY_HAVE_PTHREAD=1"}, {"backtrace": 4, "fragment": "-DFOLLY_HAVE_XSI_STRERROR_R=1"}], "defines": [{"define": "appmodules_EXPORTS"}], "includes": [{"backtrace": 9, "path": "C:/BenzochemApp/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup"}, {"backtrace": 9, "path": "C:/BenzochemApp/android/app/build/generated/autolinking/src/main/jni"}, {"backtrace": 11, "path": "D:/Benzochem Industries - Mobile/edit-1/user - mobile/BenzochemIndustries/node_modules/react-native-safe-area-context/android/src/main/jni"}, {"backtrace": 13, "path": "D:/Benzochem Industries - Mobile/edit-1/user - mobile/BenzochemIndustries/node_modules/react-native-screens/android/src/main/jni"}, {"backtrace": 15, "path": "D:/Benzochem Industries - Mobile/edit-1/user - mobile/BenzochemIndustries/node_modules/react-native-svg/android/src/main/jni"}, {"backtrace": 4, "path": "D:/Benzochem Industries - Mobile/edit-1/user - mobile/BenzochemIndustries/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/."}, {"backtrace": 4, "path": "D:/Benzochem Industries - Mobile/edit-1/user - mobile/BenzochemIndustries/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage"}, {"backtrace": 4, "path": "D:/Benzochem Industries - Mobile/edit-1/user - mobile/BenzochemIndustries/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/."}, {"backtrace": 4, "path": "D:/Benzochem Industries - Mobile/edit-1/user - mobile/BenzochemIndustries/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen"}, {"backtrace": 4, "path": "D:/Benzochem Industries - Mobile/edit-1/user - mobile/BenzochemIndustries/node_modules/react-native-image-picker/android/build/generated/source/codegen/jni/."}, {"backtrace": 4, "path": "D:/Benzochem Industries - Mobile/edit-1/user - mobile/BenzochemIndustries/node_modules/react-native-image-picker/android/build/generated/source/codegen/jni/react/renderer/components/RNImagePickerSpec"}, {"backtrace": 4, "path": "D:/Benzochem Industries - Mobile/edit-1/user - mobile/BenzochemIndustries/node_modules/react-native-permissions/android/build/generated/source/codegen/jni/."}, {"backtrace": 4, "path": "D:/Benzochem Industries - Mobile/edit-1/user - mobile/BenzochemIndustries/node_modules/react-native-permissions/android/build/generated/source/codegen/jni/react/renderer/components/RNPermissionsSpec"}, {"backtrace": 4, "path": "D:/Benzochem Industries - Mobile/edit-1/user - mobile/BenzochemIndustries/node_modules/react-native-safe-area-context/android/src/main/jni/."}, {"backtrace": 4, "path": "D:/Benzochem Industries - Mobile/edit-1/user - mobile/BenzochemIndustries/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp"}, {"backtrace": 4, "path": "D:/Benzochem Industries - Mobile/edit-1/user - mobile/BenzochemIndustries/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni"}, {"backtrace": 4, "path": "D:/Benzochem Industries - Mobile/edit-1/user - mobile/BenzochemIndustries/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext"}, {"backtrace": 4, "path": "D:/Benzochem Industries - Mobile/edit-1/user - mobile/BenzochemIndustries/node_modules/react-native-screens/android/src/main/jni/."}, {"backtrace": 4, "path": "D:/Benzochem Industries - Mobile/edit-1/user - mobile/BenzochemIndustries/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp"}, {"backtrace": 4, "path": "D:/Benzochem Industries - Mobile/edit-1/user - mobile/BenzochemIndustries/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni"}, {"backtrace": 4, "path": "D:/Benzochem Industries - Mobile/edit-1/user - mobile/BenzochemIndustries/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens"}, {"backtrace": 4, "path": "D:/Benzochem Industries - Mobile/edit-1/user - mobile/BenzochemIndustries/node_modules/react-native-svg/android/src/main/jni/."}, {"backtrace": 4, "path": "D:/Benzochem Industries - Mobile/edit-1/user - mobile/BenzochemIndustries/node_modules/react-native-svg/android/src/main/jni/../../../../common/cpp"}, {"backtrace": 4, "path": "D:/Benzochem Industries - Mobile/edit-1/user - mobile/BenzochemIndustries/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni"}, {"backtrace": 4, "path": "D:/Benzochem Industries - Mobile/edit-1/user - mobile/BenzochemIndustries/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnsvg"}, {"backtrace": 5, "isSystem": true, "path": "C:/Users/<USER>/.gradle/caches/8.14.1/transforms/b6a6ccfe82730aba80016e7d9940f54a/transformed/fbjni-0.7.0/prefab/modules/fbjni/include"}, {"backtrace": 5, "isSystem": true, "path": "C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/prefab/modules/jsi/include"}, {"backtrace": 5, "isSystem": true, "path": "C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/prefab/modules/reactnative/include"}], "language": "CXX", "sourceIndexes": [0, 1], "sysroot": {"path": "C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot"}}], "dependencies": [{"backtrace": 4, "id": "react_codegen_rnasyncstorage::@1596841e19ec5b9eeffe"}, {"backtrace": 4, "id": "react_codegen_RNImagePickerSpec::@f66ee9a2efecfb28bee4"}, {"backtrace": 4, "id": "react_codegen_safeareacontext::@7984cd80db47aa7b952a"}, {"backtrace": 4, "id": "react_codegen_rnscreens::@25bcbd507e98d3a854ad"}, {"backtrace": 4, "id": "react_codegen_RNPermissionsSpec::@7ad697819b753921c957"}, {"backtrace": 4, "id": "react_codegen_rngesturehandler_codegen::@39f233abcd2c728bc6ec"}, {"backtrace": 4, "id": "react_codegen_rnsvg::@4f40eb209d0c0b4a3b65"}], "id": "appmodules::@6890427a1f51a3e7e1df", "link": {"commandFragments": [{"fragment": "-Wl,-z,max-page-size=16384 -Wl,--build-id=sha1 -Wl,--no-rosegment -Wl,--no-undefined-version -Wl,--fatal-warnings -Wl,--no-undefined -Qunused-arguments", "role": "flags"}, {"backtrace": 4, "fragment": "C:\\BenzochemApp\\android\\app\\build\\intermediates\\cxx\\Debug\\5d6b2m5p\\obj\\arm64-v8a\\libreact_codegen_safeareacontext.so", "role": "libraries"}, {"backtrace": 4, "fragment": "C:\\BenzochemApp\\android\\app\\build\\intermediates\\cxx\\Debug\\5d6b2m5p\\obj\\arm64-v8a\\libreact_codegen_rnscreens.so", "role": "libraries"}, {"backtrace": 4, "fragment": "C:\\BenzochemApp\\android\\app\\build\\intermediates\\cxx\\Debug\\5d6b2m5p\\obj\\arm64-v8a\\libreact_codegen_rnsvg.so", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\b6a6ccfe82730aba80016e7d9940f54a\\transformed\\fbjni-0.7.0\\prefab\\modules\\fbjni\\libs\\android.arm64-v8a\\libfbjni.so", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\1d221509c521a46e74f6572064742339\\transformed\\react-android-0.80.1-debug\\prefab\\modules\\jsi\\libs\\android.arm64-v8a\\libjsi.so", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\1d221509c521a46e74f6572064742339\\transformed\\react-android-0.80.1-debug\\prefab\\modules\\reactnative\\libs\\android.arm64-v8a\\libreactnative.so", "role": "libraries"}, {"fragment": "-latomic -lm", "role": "libraries"}], "language": "CXX", "sysroot": {"path": "C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot"}}, "name": "appmodules", "nameOnDisk": "libappmodules.so", "paths": {"build": ".", "source": "."}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0, 1]}, {"name": "Object Libraries", "sourceIndexes": [2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29]}], "sources": [{"backtrace": 3, "compileGroupIndex": 0, "path": "C:/BenzochemApp/android/app/build/generated/autolinking/src/main/jni/autolinking.cpp", "sourceGroupIndex": 0}, {"backtrace": 3, "compileGroupIndex": 0, "path": "OnLoad.cpp", "sourceGroupIndex": 0}, {"backtrace": 4, "isGenerated": true, "path": "C:/BenzochemApp/android/app/.cxx/Debug/5d6b2m5p/arm64-v8a/rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/ComponentDescriptors.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "C:/BenzochemApp/android/app/.cxx/Debug/5d6b2m5p/arm64-v8a/rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/EventEmitters.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "C:/BenzochemApp/android/app/.cxx/Debug/5d6b2m5p/arm64-v8a/rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/Props.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "C:/BenzochemApp/android/app/.cxx/Debug/5d6b2m5p/arm64-v8a/rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/ShadowNodes.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "C:/BenzochemApp/android/app/.cxx/Debug/5d6b2m5p/arm64-v8a/rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/States.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "C:/BenzochemApp/android/app/.cxx/Debug/5d6b2m5p/arm64-v8a/rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/rnasyncstorageJSI-generated.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "C:/BenzochemApp/android/app/.cxx/Debug/5d6b2m5p/arm64-v8a/rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/rnasyncstorage-generated.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "C:/BenzochemApp/android/app/.cxx/Debug/5d6b2m5p/arm64-v8a/rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/ComponentDescriptors.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "C:/BenzochemApp/android/app/.cxx/Debug/5d6b2m5p/arm64-v8a/rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/EventEmitters.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "C:/BenzochemApp/android/app/.cxx/Debug/5d6b2m5p/arm64-v8a/rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/Props.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "C:/BenzochemApp/android/app/.cxx/Debug/5d6b2m5p/arm64-v8a/rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/ShadowNodes.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "C:/BenzochemApp/android/app/.cxx/Debug/5d6b2m5p/arm64-v8a/rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/States.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "C:/BenzochemApp/android/app/.cxx/Debug/5d6b2m5p/arm64-v8a/rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/rngesturehandler_codegenJSI-generated.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "C:/BenzochemApp/android/app/.cxx/Debug/5d6b2m5p/arm64-v8a/rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/rngesturehandler_codegen-generated.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "C:/BenzochemApp/android/app/.cxx/Debug/5d6b2m5p/arm64-v8a/RNImagePickerSpec_autolinked_build/CMakeFiles/react_codegen_RNImagePickerSpec.dir/RNImagePickerSpec-generated.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "C:/BenzochemApp/android/app/.cxx/Debug/5d6b2m5p/arm64-v8a/RNImagePickerSpec_autolinked_build/CMakeFiles/react_codegen_RNImagePickerSpec.dir/react/renderer/components/RNImagePickerSpec/ComponentDescriptors.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "C:/BenzochemApp/android/app/.cxx/Debug/5d6b2m5p/arm64-v8a/RNImagePickerSpec_autolinked_build/CMakeFiles/react_codegen_RNImagePickerSpec.dir/react/renderer/components/RNImagePickerSpec/EventEmitters.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "C:/BenzochemApp/android/app/.cxx/Debug/5d6b2m5p/arm64-v8a/RNImagePickerSpec_autolinked_build/CMakeFiles/react_codegen_RNImagePickerSpec.dir/react/renderer/components/RNImagePickerSpec/Props.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "C:/BenzochemApp/android/app/.cxx/Debug/5d6b2m5p/arm64-v8a/RNImagePickerSpec_autolinked_build/CMakeFiles/react_codegen_RNImagePickerSpec.dir/react/renderer/components/RNImagePickerSpec/RNImagePickerSpecJSI-generated.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "C:/BenzochemApp/android/app/.cxx/Debug/5d6b2m5p/arm64-v8a/RNImagePickerSpec_autolinked_build/CMakeFiles/react_codegen_RNImagePickerSpec.dir/react/renderer/components/RNImagePickerSpec/ShadowNodes.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "C:/BenzochemApp/android/app/.cxx/Debug/5d6b2m5p/arm64-v8a/RNImagePickerSpec_autolinked_build/CMakeFiles/react_codegen_RNImagePickerSpec.dir/react/renderer/components/RNImagePickerSpec/States.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "C:/BenzochemApp/android/app/.cxx/Debug/5d6b2m5p/arm64-v8a/RNPermissionsSpec_autolinked_build/CMakeFiles/react_codegen_RNPermissionsSpec.dir/RNPermissionsSpec-generated.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "C:/BenzochemApp/android/app/.cxx/Debug/5d6b2m5p/arm64-v8a/RNPermissionsSpec_autolinked_build/CMakeFiles/react_codegen_RNPermissionsSpec.dir/react/renderer/components/RNPermissionsSpec/ComponentDescriptors.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "C:/BenzochemApp/android/app/.cxx/Debug/5d6b2m5p/arm64-v8a/RNPermissionsSpec_autolinked_build/CMakeFiles/react_codegen_RNPermissionsSpec.dir/react/renderer/components/RNPermissionsSpec/EventEmitters.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "C:/BenzochemApp/android/app/.cxx/Debug/5d6b2m5p/arm64-v8a/RNPermissionsSpec_autolinked_build/CMakeFiles/react_codegen_RNPermissionsSpec.dir/react/renderer/components/RNPermissionsSpec/Props.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "C:/BenzochemApp/android/app/.cxx/Debug/5d6b2m5p/arm64-v8a/RNPermissionsSpec_autolinked_build/CMakeFiles/react_codegen_RNPermissionsSpec.dir/react/renderer/components/RNPermissionsSpec/RNPermissionsSpecJSI-generated.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "C:/BenzochemApp/android/app/.cxx/Debug/5d6b2m5p/arm64-v8a/RNPermissionsSpec_autolinked_build/CMakeFiles/react_codegen_RNPermissionsSpec.dir/react/renderer/components/RNPermissionsSpec/ShadowNodes.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "C:/BenzochemApp/android/app/.cxx/Debug/5d6b2m5p/arm64-v8a/RNPermissionsSpec_autolinked_build/CMakeFiles/react_codegen_RNPermissionsSpec.dir/react/renderer/components/RNPermissionsSpec/States.cpp.o", "sourceGroupIndex": 1}], "type": "SHARED_LIBRARY"}