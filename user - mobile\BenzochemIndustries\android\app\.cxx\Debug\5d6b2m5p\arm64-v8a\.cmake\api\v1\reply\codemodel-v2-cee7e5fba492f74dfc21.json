{"configurations": [{"directories": [{"build": ".", "childIndexes": [1, 2, 3, 4, 5, 6, 7], "jsonFile": "directory-.-Debug-d0094a50bb2071803777.json", "minimumCMakeVersion": {"string": "3.13"}, "projectIndex": 0, "source": ".", "targetIndexes": [0]}, {"build": "rnasyncstorage_autolinked_build", "jsonFile": "directory-rnasyncstorage_autolinked_build-Debug-acea20004fa68e88db21.json", "minimumCMakeVersion": {"string": "3.13"}, "parentIndex": 0, "projectIndex": 0, "source": "D:/Benzochem Industries - Mobile/edit-1/user - mobile/BenzochemIndustries/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni", "targetIndexes": [3]}, {"build": "rngesturehandler_codegen_autolinked_build", "jsonFile": "directory-rngesturehandler_codegen_autolinked_build-Debug-3dd4da30a0eb3e1e0c5d.json", "minimumCMakeVersion": {"string": "3.13"}, "parentIndex": 0, "projectIndex": 0, "source": "D:/Benzochem Industries - Mobile/edit-1/user - mobile/BenzochemIndustries/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni", "targetIndexes": [4]}, {"build": "RNImagePickerSpec_autolinked_build", "jsonFile": "directory-RNImagePickerSpec_autolinked_build-Debug-8ecf1a5459ce2fe130ad.json", "minimumCMakeVersion": {"string": "3.13"}, "parentIndex": 0, "projectIndex": 0, "source": "D:/Benzochem Industries - Mobile/edit-1/user - mobile/BenzochemIndustries/node_modules/react-native-image-picker/android/build/generated/source/codegen/jni", "targetIndexes": [1]}, {"build": "RNPermissionsSpec_autolinked_build", "jsonFile": "directory-RNPermissionsSpec_autolinked_build-Debug-6400a6ed68daa949ab50.json", "minimumCMakeVersion": {"string": "3.13"}, "parentIndex": 0, "projectIndex": 0, "source": "D:/Benzochem Industries - Mobile/edit-1/user - mobile/BenzochemIndustries/node_modules/react-native-permissions/android/build/generated/source/codegen/jni", "targetIndexes": [2]}, {"build": "safeareacontext_autolinked_build", "jsonFile": "directory-safeareacontext_autolinked_build-Debug-cec07eb89a6fa8d35e25.json", "minimumCMakeVersion": {"string": "3.13"}, "parentIndex": 0, "projectIndex": 0, "source": "D:/Benzochem Industries - Mobile/edit-1/user - mobile/BenzochemIndustries/node_modules/react-native-safe-area-context/android/src/main/jni", "targetIndexes": [7]}, {"build": "rnscreens_autolinked_build", "jsonFile": "directory-rnscreens_autolinked_build-Debug-fbae58b6e90755086e00.json", "minimumCMakeVersion": {"string": "3.13"}, "parentIndex": 0, "projectIndex": 0, "source": "D:/Benzochem Industries - Mobile/edit-1/user - mobile/BenzochemIndustries/node_modules/react-native-screens/android/src/main/jni", "targetIndexes": [5]}, {"build": "rnsvg_autolinked_build", "jsonFile": "directory-rnsvg_autolinked_build-Debug-daaa95eda27fef6cece2.json", "minimumCMakeVersion": {"string": "3.13"}, "parentIndex": 0, "projectIndex": 0, "source": "D:/Benzochem Industries - Mobile/edit-1/user - mobile/BenzochemIndustries/node_modules/react-native-svg/android/src/main/jni", "targetIndexes": [6]}], "name": "Debug", "projects": [{"directoryIndexes": [0, 1, 2, 3, 4, 5, 6, 7], "name": "appmodules", "targetIndexes": [0, 1, 2, 3, 4, 5, 6, 7]}], "targets": [{"directoryIndex": 0, "id": "appmodules::@6890427a1f51a3e7e1df", "jsonFile": "target-appmodules-Debug-e73fea0d79a60c956f72.json", "name": "appmodules", "projectIndex": 0}, {"directoryIndex": 3, "id": "react_codegen_RNImagePickerSpec::@f66ee9a2efecfb28bee4", "jsonFile": "target-react_codegen_RNImagePickerSpec-Debug-22b341e76ea2ecef3f00.json", "name": "react_codegen_RNImagePickerSpec", "projectIndex": 0}, {"directoryIndex": 4, "id": "react_codegen_RNPermissionsSpec::@7ad697819b753921c957", "jsonFile": "target-react_codegen_RNPermissionsSpec-Debug-0bd1718bcd928f418af1.json", "name": "react_codegen_RNPermissionsSpec", "projectIndex": 0}, {"directoryIndex": 1, "id": "react_codegen_rnasyncstorage::@1596841e19ec5b9eeffe", "jsonFile": "target-react_codegen_rnasyncstorage-Debug-d3df098639a1b6f22cd7.json", "name": "react_codegen_rnasyncstorage", "projectIndex": 0}, {"directoryIndex": 2, "id": "react_codegen_rngesturehandler_codegen::@39f233abcd2c728bc6ec", "jsonFile": "target-react_codegen_rngesturehandler_codegen-Debug-9f6bc14824ec9effe4f3.json", "name": "react_codegen_rngesturehandler_codegen", "projectIndex": 0}, {"directoryIndex": 6, "id": "react_codegen_rnscreens::@25bcbd507e98d3a854ad", "jsonFile": "target-react_codegen_rnscreens-Debug-815049ee192c8dc8322f.json", "name": "react_codegen_rnscreens", "projectIndex": 0}, {"directoryIndex": 7, "id": "react_codegen_rnsvg::@4f40eb209d0c0b4a3b65", "jsonFile": "target-react_codegen_rnsvg-Debug-276b0ed1ec187075f91a.json", "name": "react_codegen_rnsvg", "projectIndex": 0}, {"directoryIndex": 5, "id": "react_codegen_safeareacontext::@7984cd80db47aa7b952a", "jsonFile": "target-react_codegen_safeareacontext-Debug-bcfa58d618ba5451903c.json", "name": "react_codegen_safeareacontext", "projectIndex": 0}]}], "kind": "codemodel", "paths": {"build": "C:/BenzochemApp/android/app/.cxx/Debug/5d6b2m5p/arm64-v8a", "source": "C:/BenzochemApp/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup"}, "version": {"major": 2, "minor": 3}}