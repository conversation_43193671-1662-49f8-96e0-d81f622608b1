import React, { useState } from 'react';
import { View, Text, TouchableOpacity, ScrollView, StyleSheet, Alert } from 'react-native';
import { ADMIN_API_CONFIG, REQUEST_CONFIG } from '../config';

interface TestResult {
  test: string;
  status: 'pending' | 'success' | 'error';
  message: string;
  details?: any;
}

export const DebugApiTest: React.FC = () => {
  const [results, setResults] = useState<TestResult[]>([]);
  const [isRunning, setIsRunning] = useState(false);

  const addResult = (result: TestResult) => {
    setResults(prev => [...prev, result]);
  };

  const clearResults = () => {
    setResults([]);
  };

  const testApiConnectivity = async () => {
    setIsRunning(true);
    clearResults();

    // Test 1: Check Admin API Status
    addResult({ test: 'Admin API Status', status: 'pending', message: 'Testing...' });
    try {
      const statusUrl = `${ADMIN_API_CONFIG.FULL_URL}/status`;
      console.log('Testing status URL:', statusUrl);
      
      const response = await fetch(statusUrl, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (response.ok) {
        const data = await response.json();
        addResult({
          test: 'Admin API Status',
          status: 'success',
          message: `API is reachable (${response.status})`,
          details: data.data
        });
      } else {
        addResult({
          test: 'Admin API Status',
          status: 'error',
          message: `HTTP ${response.status}: ${response.statusText}`,
        });
      }
    } catch (error) {
      addResult({
        test: 'Admin API Status',
        status: 'error',
        message: `Network error: ${error.message}`,
      });
    }

    // Test 2: Test Products API with API Key
    addResult({ test: 'Products API', status: 'pending', message: 'Testing...' });
    try {
      const productsUrl = `${ADMIN_API_CONFIG.FULL_URL}/products?limit=8&featured=true`;
      console.log('Testing products URL:', productsUrl);
      console.log('Using API key:', ADMIN_API_CONFIG.API_KEY ? 'Present' : 'Missing');
      
      const response = await fetch(productsUrl, {
        method: 'GET',
        headers: REQUEST_CONFIG.ADMIN_HEADERS,
      });

      if (response.ok) {
        const data = await response.json();
        addResult({
          test: 'Products API',
          status: 'success',
          message: `Products fetched successfully (${data.data?.length || 0} items)`,
          details: { count: data.data?.length, success: data.success }
        });
      } else {
        const errorText = await response.text();
        addResult({
          test: 'Products API',
          status: 'error',
          message: `HTTP ${response.status}: ${response.statusText}`,
          details: errorText.substring(0, 200)
        });
      }
    } catch (error) {
      addResult({
        test: 'Products API',
        status: 'error',
        message: `Network error: ${error.message}`,
      });
    }

    // Test 3: Test CORS
    addResult({ test: 'CORS Configuration', status: 'pending', message: 'Testing...' });
    try {
      const corsUrl = `${ADMIN_API_CONFIG.FULL_URL}/products`;
      
      const response = await fetch(corsUrl, {
        method: 'OPTIONS',
        headers: {
          'Origin': 'http://localhost:3000',
          'Access-Control-Request-Method': 'GET',
          'Access-Control-Request-Headers': 'Content-Type, X-API-Key',
        },
      });

      const corsHeaders = {
        origin: response.headers.get('Access-Control-Allow-Origin'),
        methods: response.headers.get('Access-Control-Allow-Methods'),
        headers: response.headers.get('Access-Control-Allow-Headers'),
      };

      if (response.ok) {
        addResult({
          test: 'CORS Configuration',
          status: 'success',
          message: 'CORS is properly configured',
          details: corsHeaders
        });
      } else {
        addResult({
          test: 'CORS Configuration',
          status: 'error',
          message: `CORS preflight failed (${response.status})`,
          details: corsHeaders
        });
      }
    } catch (error) {
      addResult({
        test: 'CORS Configuration',
        status: 'error',
        message: `CORS test failed: ${error.message}`,
      });
    }

    // Test 4: Test API Key Validation
    addResult({ test: 'API Key Validation', status: 'pending', message: 'Testing...' });
    try {
      const response = await fetch(`${ADMIN_API_CONFIG.FULL_URL}/products?limit=1`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'X-API-Key': 'invalid_key_test',
        },
      });

      if (response.status === 401) {
        addResult({
          test: 'API Key Validation',
          status: 'success',
          message: 'API key validation is working (401 for invalid key)',
        });
      } else {
        addResult({
          test: 'API Key Validation',
          status: 'error',
          message: `Unexpected response for invalid key: ${response.status}`,
        });
      }
    } catch (error) {
      addResult({
        test: 'API Key Validation',
        status: 'error',
        message: `API key test failed: ${error.message}`,
      });
    }

    setIsRunning(false);
  };

  const showDetails = (result: TestResult) => {
    if (result.details) {
      Alert.alert(
        `${result.test} Details`,
        JSON.stringify(result.details, null, 2),
        [{ text: 'OK' }]
      );
    }
  };

  return (
    <View style={styles.container}>
      <Text style={styles.title}>API Debug Test</Text>
      
      <View style={styles.configInfo}>
        <Text style={styles.configTitle}>Configuration:</Text>
        <Text style={styles.configText}>Admin API: {ADMIN_API_CONFIG.FULL_URL}</Text>
        <Text style={styles.configText}>API Key: {ADMIN_API_CONFIG.API_KEY ? 'Configured' : 'Missing'}</Text>
      </View>

      <TouchableOpacity
        style={[styles.button, isRunning && styles.buttonDisabled]}
        onPress={testApiConnectivity}
        disabled={isRunning}
      >
        <Text style={styles.buttonText}>
          {isRunning ? 'Running Tests...' : 'Run API Tests'}
        </Text>
      </TouchableOpacity>

      <ScrollView style={styles.resultsContainer}>
        {results.map((result, index) => (
          <TouchableOpacity
            key={index}
            style={[
              styles.resultItem,
              result.status === 'success' && styles.resultSuccess,
              result.status === 'error' && styles.resultError,
            ]}
            onPress={() => showDetails(result)}
          >
            <Text style={styles.resultTest}>{result.test}</Text>
            <Text style={styles.resultMessage}>{result.message}</Text>
            {result.details && <Text style={styles.resultDetails}>Tap for details</Text>}
          </TouchableOpacity>
        ))}
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 20,
    backgroundColor: '#f5f5f5',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 20,
    textAlign: 'center',
  },
  configInfo: {
    backgroundColor: '#e3f2fd',
    padding: 15,
    borderRadius: 8,
    marginBottom: 20,
  },
  configTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 5,
  },
  configText: {
    fontSize: 12,
    color: '#666',
    marginBottom: 2,
  },
  button: {
    backgroundColor: '#2196f3',
    padding: 15,
    borderRadius: 8,
    marginBottom: 20,
  },
  buttonDisabled: {
    backgroundColor: '#ccc',
  },
  buttonText: {
    color: 'white',
    textAlign: 'center',
    fontSize: 16,
    fontWeight: 'bold',
  },
  resultsContainer: {
    flex: 1,
  },
  resultItem: {
    backgroundColor: 'white',
    padding: 15,
    borderRadius: 8,
    marginBottom: 10,
    borderLeftWidth: 4,
    borderLeftColor: '#ccc',
  },
  resultSuccess: {
    borderLeftColor: '#4caf50',
  },
  resultError: {
    borderLeftColor: '#f44336',
  },
  resultTest: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 5,
  },
  resultMessage: {
    fontSize: 14,
    color: '#666',
  },
  resultDetails: {
    fontSize: 12,
    color: '#2196f3',
    marginTop: 5,
    fontStyle: 'italic',
  },
});
