/**
 * Debug script to test API connectivity
 * Run this with: node debug-api.js
 *
 * This tests the deployed admin API that the mobile app (running on physical device) connects to
 */

const fetch = require('node-fetch');

// Configuration from mobile app .env file
// The admin project is deployed at this URL (was localhost:3001 before deployment)
const ADMIN_API_BASE_URL = 'https://apibenzochem.vercel.app';
const ADMIN_API_KEY = 'bzk_live_nGkfHjbSjzjy8Ex76T32F54tcx8RgRUQ';

async function testApiConnectivity() {
  console.log('🔍 Testing API Connectivity...\n');
  
  // Test 1: Check if admin API is reachable
  console.log('1. Testing Admin API Status...');
  try {
    const statusResponse = await fetch(`${ADMIN_API_BASE_URL}/api/v1/status`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });
    
    console.log(`   Status: ${statusResponse.status} ${statusResponse.statusText}`);
    
    if (statusResponse.ok) {
      const statusData = await statusResponse.json();
      console.log('   ✅ Admin API is reachable');
      console.log(`   Version: ${statusData.data?.version || 'Unknown'}`);
      console.log(`   Environment: ${statusData.data?.environment || 'Unknown'}`);
    } else {
      console.log('   ❌ Admin API status check failed');
    }
  } catch (error) {
    console.log('   ❌ Admin API is not reachable');
    console.log(`   Error: ${error.message}`);
  }
  
  console.log('\n2. Testing Products API with API Key...');
  try {
    const productsResponse = await fetch(`${ADMIN_API_BASE_URL}/api/v1/products?limit=8&featured=true`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'X-API-Key': ADMIN_API_KEY,
      },
    });
    
    console.log(`   Status: ${productsResponse.status} ${productsResponse.statusText}`);
    
    if (productsResponse.ok) {
      const productsData = await productsResponse.json();
      console.log('   ✅ Products API is working');
      console.log(`   Products returned: ${productsData.data?.length || 0}`);
      console.log(`   Success: ${productsData.success}`);
    } else {
      console.log('   ❌ Products API failed');
      const errorData = await productsResponse.text();
      console.log(`   Error response: ${errorData.substring(0, 200)}...`);
    }
  } catch (error) {
    console.log('   ❌ Products API request failed');
    console.log(`   Error: ${error.message}`);
  }
  
  console.log('\n3. Testing CORS Headers (for mobile app)...');
  try {
    const corsResponse = await fetch(`${ADMIN_API_BASE_URL}/api/v1/products`, {
      method: 'OPTIONS',
      headers: {
        'Origin': 'capacitor://localhost', // Common origin for mobile apps
        'Access-Control-Request-Method': 'GET',
        'Access-Control-Request-Headers': 'Content-Type, X-API-Key',
      },
    });
    
    console.log(`   Status: ${corsResponse.status} ${corsResponse.statusText}`);
    console.log('   CORS Headers:');
    console.log(`     Access-Control-Allow-Origin: ${corsResponse.headers.get('Access-Control-Allow-Origin')}`);
    console.log(`     Access-Control-Allow-Methods: ${corsResponse.headers.get('Access-Control-Allow-Methods')}`);
    console.log(`     Access-Control-Allow-Headers: ${corsResponse.headers.get('Access-Control-Allow-Headers')}`);
    
    if (corsResponse.ok) {
      console.log('   ✅ CORS is configured');
    } else {
      console.log('   ❌ CORS preflight failed');
    }
  } catch (error) {
    console.log('   ❌ CORS test failed');
    console.log(`   Error: ${error.message}`);
  }
  
  console.log('\n4. Testing API Key Validation...');
  try {
    const invalidKeyResponse = await fetch(`${ADMIN_API_BASE_URL}/api/v1/products?limit=1`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'X-API-Key': 'invalid_key',
      },
    });
    
    console.log(`   Status with invalid key: ${invalidKeyResponse.status} ${invalidKeyResponse.statusText}`);
    
    if (invalidKeyResponse.status === 401) {
      console.log('   ✅ API key validation is working (401 for invalid key)');
    } else {
      console.log('   ⚠️  Unexpected response for invalid API key');
    }
  } catch (error) {
    console.log('   ❌ API key validation test failed');
    console.log(`   Error: ${error.message}`);
  }
  
  console.log('\n📋 Summary:');
  console.log(`   Admin API URL: ${ADMIN_API_BASE_URL}`);
  console.log(`   API Key: ${ADMIN_API_KEY.substring(0, 10)}...`);
  console.log('   Test completed. Check the results above for any issues.');
}

// Run the test
testApiConnectivity().catch(console.error);
