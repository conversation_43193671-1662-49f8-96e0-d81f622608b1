<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Email Templates Preview - Benzochem Industries</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f8fafc;
            color: #1a202c;
            line-height: 1.6;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            text-align: center;
            margin-bottom: 40px;
            padding: 40px 20px;
            background: linear-gradient(135deg, #4a7c59 0%, #5a8a68 100%);
            color: white;
            border-radius: 12px;
        }
        
        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
            font-weight: 700;
        }
        
        .header p {
            font-size: 1.2rem;
            opacity: 0.9;
        }
        
        .templates-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 30px;
            margin-bottom: 40px;
        }
        
        .template-card {
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            border: 1px solid #e2e8f0;
        }
        
        .template-header {
            padding: 20px;
            border-bottom: 1px solid #e2e8f0;
        }
        
        .template-title {
            font-size: 1.25rem;
            font-weight: 600;
            margin-bottom: 5px;
            color: #1a202c;
        }
        
        .template-description {
            color: #64748b;
            font-size: 0.9rem;
        }
        
        .template-preview {
            height: 400px;
            overflow: hidden;
            position: relative;
        }
        
        .template-iframe {
            width: 100%;
            height: 100%;
            border: none;
            transform: scale(0.8);
            transform-origin: top left;
            width: 125%;
            height: 125%;
        }
        
        .template-actions {
            padding: 15px 20px;
            background: #f8fafc;
            border-top: 1px solid #e2e8f0;
        }
        
        .btn {
            display: inline-block;
            padding: 8px 16px;
            background: #4a7c59;
            color: white;
            text-decoration: none;
            border-radius: 6px;
            font-size: 0.875rem;
            font-weight: 500;
            transition: background 0.2s;
            margin-right: 10px;
        }
        
        .btn:hover {
            background: #3d6b4a;
        }
        
        .btn-secondary {
            background: #64748b;
        }
        
        .btn-secondary:hover {
            background: #475569;
        }
        
        .navigation {
            position: fixed;
            top: 20px;
            right: 20px;
            background: white;
            border-radius: 8px;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            padding: 15px;
            z-index: 1000;
        }
        
        .nav-title {
            font-weight: 600;
            margin-bottom: 10px;
            color: #1a202c;
        }
        
        .nav-link {
            display: block;
            color: #4a7c59;
            text-decoration: none;
            padding: 5px 0;
            font-size: 0.875rem;
            border-bottom: 1px solid #e2e8f0;
        }
        
        .nav-link:last-child {
            border-bottom: none;
        }
        
        .nav-link:hover {
            color: #3d6b4a;
        }
        
        .info-section {
            background: white;
            border-radius: 12px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 2px 4px -1px rgba(0, 0, 0, 0.1);
        }
        
        .info-title {
            font-size: 1.5rem;
            font-weight: 600;
            margin-bottom: 15px;
            color: #1a202c;
        }
        
        .info-text {
            color: #64748b;
            margin-bottom: 15px;
        }
        
        .template-list {
            list-style: none;
            padding-left: 0;
        }
        
        .template-list li {
            padding: 8px 0;
            border-bottom: 1px solid #e2e8f0;
            color: #4a7c59;
            font-weight: 500;
        }
        
        .template-list li:last-child {
            border-bottom: none;
        }
        
        @media (max-width: 768px) {
            .navigation {
                position: static;
                margin-bottom: 20px;
            }
            
            .templates-grid {
                grid-template-columns: 1fr;
            }
            
            .header h1 {
                font-size: 2rem;
            }
        }
    </style>
</head>
<body>
    <div class="navigation">
        <div class="nav-title">Quick Navigation</div>
        <a href="#processing" class="nav-link">Processing Email</a>
        <a href="#quoted" class="nav-link">Quoted Email</a>
        <a href="#accepted" class="nav-link">Accepted Email</a>
        <a href="#rejected" class="nav-link">Rejected Email</a>
        <a href="#expired" class="nav-link">Expired Email</a>
        <a href="#reminder" class="nav-link">Reminder Email</a>
        <a href="#admin-notification" class="nav-link">Admin Notification</a>
    </div>

    <div class="container">
        <div class="header">
            <h1>📧 Email Templates Preview</h1>
            <p>Benzochem Industries - All Email Template Designs</p>
        </div>

        <div class="info-section">
            <h2 class="info-title">📋 Template Overview</h2>
            <p class="info-text">
                This preview showcases all email templates used in the Benzochem Industries admin system. 
                Each template is designed for different quotation statuses and customer interactions.
            </p>
            <ul class="template-list">
                <li>🔄 Processing Email - Sent when quotation is being processed</li>
                <li>💰 Quoted Email - Sent when quotation is ready with pricing</li>
                <li>✅ Accepted Email - Sent when customer accepts the quotation</li>
                <li>❌ Rejected Email - Sent when quotation cannot be fulfilled</li>
                <li>⏰ Expired Email - Sent when quotation validity expires</li>
                <li>🔔 Reminder Email - Sent to remind customers about pending quotations</li>
                <li>👨‍💼 Admin Notification - Sent to admin when new quotation is received</li>
            </ul>
        </div>

        <div class="templates-grid">
            <!-- Processing Email Template -->
            <div class="template-card" id="processing">
                <div class="template-header">
                    <div class="template-title">🔄 Processing Email</div>
                    <div class="template-description">Sent when quotation request is being processed by the team</div>
                </div>
                <div class="template-preview">
                    <iframe class="template-iframe" srcdoc='<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Quotation Processing - Benzochem Industries</title>
</head>
<body style="margin: 0; padding: 0; font-family: &quot;Inter&quot;, -apple-system, BlinkMacSystemFont, &quot;Segoe UI&quot;, Roboto, sans-serif; line-height: 1.6; color: #3d4852; background-color: #f5f3f0;">
    
    <table role="presentation" cellspacing="0" cellpadding="0" border="0" width="100%" style="margin: 0; padding: 20px 0; background-color: #f5f3f0;">
        <tr>
            <td style="padding: 0;">
                <table role="presentation" cellspacing="0" cellpadding="0" border="0" width="100%" style="max-width: 600px; margin: 0 auto; background-color: #faf8f5; border-radius: 12px; box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06); border: 1px solid #e8e3dc;">
                    
                    <!-- Header -->
                    <tr>
                        <td style="background: linear-gradient(135deg, #4a7c59 0%, #5a8a68 100%); padding: 40px 32px; text-align: center; color: white; border-radius: 12px 12px 0 0;">
                            <div style="width: 48px; height: 48px; background: rgba(255, 255, 255, 0.2); border-radius: 12px; display: inline-flex; align-items: center; justify-content: center; margin-bottom: 16px; font-size: 24px; font-weight: 700; color: white;">B</div>
                            <h1 style="margin: 0 0 8px 0; font-size: 24px; font-weight: 600;">Benzochem Industries</h1>
                            <p style="margin: 0; font-size: 16px; opacity: 0.9; font-weight: 400;">Processing your quotation request</p>
                        </td>
                    </tr>
                    
                    <!-- Content -->
                    <tr>
                        <td style="padding: 40px 32px;">
                            <h2 style="margin: 0 0 16px 0; font-size: 18px; font-weight: 600; color: #3d4852;">Hello John Doe,</h2>
                            
                            <p style="margin: 0 0 24px 0; font-size: 16px; color: #6b7280; line-height: 1.7;">
                                We have received your quotation request and our team is currently processing it. We will get back to you with a detailed quote soon.
                            </p>
                            
                            <div style="background: #f0f4f8; border: 1px solid #e8e3dc; border-radius: 8px; padding: 20px; margin: 24px 0;">
                                <div style="font-size: 16px; font-weight: 600; color: #4a7c59; margin-bottom: 16px;">Requested Products:</div>
                                <ul style="margin: 0; padding-left: 20px; color: #6b7280;">
                                    <li><strong>Sodium Chloride</strong> - 100 kg (Industrial Grade)</li>
                                    <li><strong>Potassium Hydroxide</strong> - 50 kg (Laboratory Grade)</li>
                                </ul>
                            </div>
                            
                            <div style="text-align: center; margin: 32px 0;">
                                <a href="#" style="display: inline-block; background: linear-gradient(135deg, #4a7c59 0%, #5a8a68 100%); color: white; text-decoration: none; padding: 16px 32px; border-radius: 8px; font-weight: 600; font-size: 16px; box-shadow: 0 4px 6px -1px rgba(74, 124, 89, 0.3);">View Request Status</a>
                            </div>
                            
                            <p style="margin: 0; font-size: 14px; color: #6b7280;">
                                Thank you for your patience. We&apos;ll notify you as soon as your quotation is ready.
                            </p>
                        </td>
                    </tr>
                    
                    <!-- Footer -->
                    <tr>
                        <td style="background: #f0f4f8; padding: 32px; text-align: center; border-top: 1px solid #e8e3dc; border-radius: 0 0 12px 12px;">
                            <div style="font-size: 14px; color: #6b7280; line-height: 1.6;">
                                <strong style="font-weight: 700; color: #3d4852;">Benzochem Industries</strong><br>
                                Premium Chemical Solutions & Trading<br>
                                E-45 Jitali Road<br>
                                Phone: +91 83206 67594<br>
                                Email: <EMAIL>
                            </div>
                            
                            <div style="margin-top: 20px; font-size: 12px; color: #9ca3af;">
                                © 2024 Benzochem Industries. All rights reserved.
                            </div>
                        </td>
                    </tr>
                </table>
            </td>
        </tr>
    </table>
</body>
</html>'></iframe>
                </div>
                <div class="template-actions">
                    <a href="#" class="btn" onclick="openFullPreview('processing')">Full Preview</a>
                    <a href="#" class="btn btn-secondary" onclick="copyTemplate('processing')">Copy HTML</a>
                </div>
            </div>

            <!-- Quoted Email Template -->
            <div class="template-card" id="quoted">
                <div class="template-header">
                    <div class="template-title">💰 Quoted Email</div>
                    <div class="template-description">Sent when quotation is ready with detailed pricing and GST breakdown</div>
                </div>
                <div class="template-preview">
                    <iframe class="template-iframe" srcdoc='<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Your Quotation is Ready - Benzochem Industries</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { 
            font-family: -apple-system, BlinkMacSystemFont, &quot;SF Pro Display&quot;, &quot;Segoe UI&quot;, Roboto, Helvetica, Arial, sans-serif;
            line-height: 1.6; 
            color: #1d1d1f;
            background: #ffffff;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
            margin: 0;
            padding: 0;
        }
        .email-container { 
            max-width: 600px;
            margin: 0 auto;
            background: #ffffff;
        }
        .header { 
            background: #000000;
            padding: 40px 32px;
            text-align: center;
            color: white;
        }
        .logo { 
            width: 60px; 
            height: 60px; 
            background: #ffffff;
            border-radius: 12px;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            margin-bottom: 16px;
            color: #000000;
            font-weight: 600;
        }
        .header h1 { 
            font-size: 24px; 
            font-weight: 600; 
            margin-bottom: 4px;
            letter-spacing: -0.5px;
        }
        .header .subtitle { 
            font-size: 16px; 
            opacity: 0.8;
            font-weight: 400;
        }
        .content { 
            padding: 40px 32px;
        }
        .greeting { 
            font-size: 28px; 
            font-weight: 600; 
            margin-bottom: 16px;
            color: #1d1d1f;
            letter-spacing: -0.5px;
        }
        .main-text { 
            font-size: 17px; 
            color: #86868b;
            margin-bottom: 32px;
            line-height: 1.5;
            font-weight: 400;
        }
        .quote-card { 
            background: #f5f5f7;
            border-radius: 16px;
            padding: 32px;
            margin: 32px 0;
        }
        .quote-title {
            font-size: 20px;
            font-weight: 600;
            color: #1d1d1f;
            margin-bottom: 24px;
            text-align: center;
        }
        .final-amount {
            text-align: center;
            margin: 24px 0;
            padding: 24px;
            background: #ffffff;
            border-radius: 12px;
            border: 1px solid #e5e5e7;
        }
        .final-label {
            font-size: 16px;
            color: #86868b;
            margin-bottom: 8px;
        }
        .final-price {
            font-size: 32px;
            font-weight: 600;
            color: #1d1d1f;
            letter-spacing: -1px;
        }
        .cta-button { 
            display: inline-block;
            background: #007aff;
            color: white;
            padding: 16px 32px;
            text-decoration: none;
            border-radius: 12px;
            font-weight: 600;
            font-size: 16px;
        }
        .footer { 
            background: #f5f5f7;
            padding: 32px;
            text-align: center;
            border-top: 1px solid #e5e5e7;
        }
    </style>
</head>
<body>
    <div class="email-container">
        <div class="header">
            <div class="logo">BC</div>
            <h1>Benzochem Industries</h1>
            <div class="subtitle">Your quotation is ready</div>
        </div>
        
        <div class="content">
            <div class="greeting">Hello John Doe,</div>
            
            <div class="main-text">
                We&apos;ve prepared your custom quotation with detailed pricing and specifications. 
                Everything is ready for your review.
            </div>
            
            <div class="quote-card">
                <div class="quote-title">Quotation Summary</div>
                
                <div class="final-amount">
                    <div class="final-label">Total Amount</div>
                    <div class="final-price">₹15,750</div>
                </div>
                
                <div style="text-align: center; font-size: 15px; color: #86868b; margin: 16px 0;">
                    Valid until December 31, 2024
                </div>
            </div>
            
            <div style="text-align: center; margin: 40px 0;">
                <a href="#" class="cta-button">
                    View Full Quotation
                </a>
            </div>
        </div>
        
        <div class="footer">
            <div style="font-size: 13px; color: #86868b;">
                © 2024 <span style="font-weight: 600; color: #1d1d1f;">Benzochem Industries</span>
            </div>
        </div>
    </div>
</body>
</html>'></iframe>
                </div>
                <div class="template-actions">
                    <a href="#" class="btn" onclick="openFullPreview('quoted')">Full Preview</a>
                    <a href="#" class="btn btn-secondary" onclick="copyTemplate('quoted')">Copy HTML</a>
                </div>
            </div>

            <!-- Accepted Email Template -->
            <div class="template-card" id="accepted">
                <div class="template-header">
                    <div class="template-title">✅ Accepted Email</div>
                    <div class="template-description">Sent when customer accepts the quotation - includes next steps</div>
                </div>
                <div class="template-preview">
                    <iframe class="template-iframe" srcdoc='<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Quotation Accepted - Benzochem Industries</title>
</head>
<body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px;">
    <div style="background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%); padding: 30px; text-align: center; border-radius: 10px 10px 0 0;">
        <h1 style="color: white; margin: 0; font-size: 28px;">Benzochem Industries</h1>
        <p style="color: white; margin: 10px 0 0 0; opacity: 0.9;">Quotation Accepted - Next Steps</p>
    </div>
    
    <div style="background: white; padding: 40px; border: 1px solid #e2e8f0; border-top: none; border-radius: 0 0 10px 10px;">
        <h2 style="color: #1a202c; margin-bottom: 20px;">Thank you, John Doe!</h2>
        
        <p style="font-size: 16px; margin-bottom: 25px;">
            Thank you for accepting our quotation! We are pleased to move forward with your order.
        </p>
        
        <div style="background: #eff6ff; border: 1px solid #bfdbfe; padding: 25px; border-radius: 8px; margin: 25px 0;">
            <h3 style="color: #1e40af; margin-bottom: 15px;">Next Steps:</h3>
            <ul style="margin: 0; padding-left: 20px; color: #1e40af;">
                <li>Our team will contact you shortly</li>
                <li>Payment and delivery arrangements will be discussed</li>
                <li>Order processing will begin immediately</li>
            </ul>
        </div>
        
        <div style="background: #f8fafc; padding: 25px; border-radius: 8px; margin: 25px 0;">
            <h3 style="color: #1a202c; margin-bottom: 15px;">Accepted Products:</h3>
            <ul style="margin: 0; padding-left: 20px;">
                <li><strong>Sodium Chloride</strong> - 100 kg (Industrial Grade)</li>
                <li><strong>Potassium Hydroxide</strong> - 50 kg (Laboratory Grade)</li>
            </ul>
        </div>
        
        <div style="text-align: center; margin: 30px 0;">
            <a href="#" style="background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%); color: white; padding: 12px 30px; text-decoration: none; border-radius: 6px; font-weight: bold;">Track Your Order</a>
        </div>
        
        <p style="color: #64748b; font-size: 14px; margin-top: 30px;">
            We appreciate your business and look forward to serving you!
        </p>
    </div>
    
    <div style="text-align: center; padding: 20px; color: #64748b; font-size: 12px;">
        © 2024 Benzochem Industries. All rights reserved.
    </div>
</body>
</html>'></iframe>
                </div>
                <div class="template-actions">
                    <a href="#" class="btn" onclick="openFullPreview('accepted')">Full Preview</a>
                    <a href="#" class="btn btn-secondary" onclick="copyTemplate('accepted')">Copy HTML</a>
                </div>
            </div>

            <!-- Rejected Email Template -->
            <div class="template-card" id="rejected">
                <div class="template-header">
                    <div class="template-title">❌ Rejected Email</div>
                    <div class="template-description">Sent when quotation request cannot be fulfilled</div>
                </div>
                <div class="template-preview">
                    <iframe class="template-iframe" srcdoc='<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Quotation Update - Benzochem Industries</title>
</head>
<body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px;">
    <div style="background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%); padding: 30px; text-align: center; border-radius: 10px 10px 0 0;">
        <h1 style="color: white; margin: 0; font-size: 28px;">Benzochem Industries</h1>
        <p style="color: white; margin: 10px 0 0 0; opacity: 0.9;">Quotation Update</p>
    </div>
    
    <div style="background: white; padding: 40px; border: 1px solid #e2e8f0; border-top: none; border-radius: 0 0 10px 10px;">
        <h2 style="color: #1a202c; margin-bottom: 20px;">Hello John Doe,</h2>
        
        <p style="font-size: 16px; margin-bottom: 25px;">
            We regret to inform you that we are unable to fulfill your quotation request at this time.
        </p>
        
        <div style="background: #f8fafc; padding: 25px; border-radius: 8px; margin: 25px 0;">
            <h3 style="color: #1a202c; margin-bottom: 15px;">Requested Products:</h3>
            <ul style="margin: 0; padding-left: 20px;">
                <li><strong>Sodium Chloride</strong> - 100 kg (Industrial Grade)</li>
                <li><strong>Potassium Hydroxide</strong> - 50 kg (Laboratory Grade)</li>
            </ul>
        </div>
        
        <div style="text-align: center; margin: 30px 0;">
            <a href="mailto:<EMAIL>" style="background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%); color: white; padding: 12px 30px; text-decoration: none; border-radius: 6px; font-weight: bold;">Contact Support</a>
        </div>
        
        <p style="color: #64748b; font-size: 14px; margin-top: 30px;">
            If you have any questions or would like to discuss alternative options, please feel free to contact us.
        </p>
    </div>
    
    <div style="text-align: center; padding: 20px; color: #64748b; font-size: 12px;">
        © 2024 Benzochem Industries. All rights reserved.
    </div>
</body>
</html>'></iframe>
                </div>
                <div class="template-actions">
                    <a href="#" class="btn" onclick="openFullPreview('rejected')">Full Preview</a>
                    <a href="#" class="btn btn-secondary" onclick="copyTemplate('rejected')">Copy HTML</a>
                </div>
            </div>

            <!-- Expired Email Template -->
            <div class="template-card" id="expired">
                <div class="template-header">
                    <div class="template-title">⏰ Expired Email</div>
                    <div class="template-description">Sent when quotation validity period expires</div>
                </div>
                <div class="template-preview">
                    <iframe class="template-iframe" srcdoc='<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Quotation Expired - Benzochem Industries</title>
</head>
<body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px;">
    <div style="background: linear-gradient(135deg, #6b7280 0%, #4b5563 100%); padding: 30px; text-align: center; border-radius: 10px 10px 0 0;">
        <h1 style="color: white; margin: 0; font-size: 28px;">Benzochem Industries</h1>
        <p style="color: white; margin: 10px 0 0 0; opacity: 0.9;">Quotation Expired</p>
    </div>
    
    <div style="background: white; padding: 40px; border: 1px solid #e2e8f0; border-top: none; border-radius: 0 0 10px 10px;">
        <h2 style="color: #1a202c; margin-bottom: 20px;">Hello John Doe,</h2>
        
        <p style="font-size: 16px; margin-bottom: 25px;">
            Your quotation has expired. If you are still interested in the products, please submit a new quotation request and we will be happy to provide you with updated pricing.
        </p>
        
        <div style="background: #f8fafc; padding: 25px; border-radius: 8px; margin: 25px 0;">
            <h3 style="color: #1a202c; margin-bottom: 15px;">Expired Products:</h3>
            <ul style="margin: 0; padding-left: 20px;">
                <li><strong>Sodium Chloride</strong> - 100 kg (Industrial Grade)</li>
                <li><strong>Potassium Hydroxide</strong> - 50 kg (Laboratory Grade)</li>
            </ul>
        </div>
        
        <div style="text-align: center; margin: 30px 0;">
            <a href="#" style="background: linear-gradient(135deg, #6b7280 0%, #4b5563 100%); color: white; padding: 12px 30px; text-decoration: none; border-radius: 6px; font-weight: bold;">Submit New Request</a>
        </div>
        
        <p style="color: #64748b; font-size: 14px; margin-top: 30px;">
            Thank you for your interest in Benzochem Industries.
        </p>
    </div>
    
    <div style="text-align: center; padding: 20px; color: #64748b; font-size: 12px;">
        © 2024 Benzochem Industries. All rights reserved.
    </div>
</body>
</html>'></iframe>
                </div>
                <div class="template-actions">
                    <a href="#" class="btn" onclick="openFullPreview('expired')">Full Preview</a>
                    <a href="#" class="btn btn-secondary" onclick="copyTemplate('expired')">Copy HTML</a>
                </div>
            </div>

            <!-- Reminder Email Template -->
            <div class="template-card" id="reminder">
                <div class="template-header">
                    <div class="template-title">🔔 Reminder Email</div>
                    <div class="template-description">Sent to remind customers about pending quotations</div>
                </div>
                <div class="template-preview">
                    <iframe class="template-iframe" srcdoc='<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Quotation Reminder - Benzochem Industries</title>
</head>
<body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px;">
    <div style="background: linear-gradient(135deg, #8b5cf6 0%, #6d28d9 100%); padding: 30px; text-align: center; border-radius: 10px 10px 0 0;">
        <h1 style="color: white; margin: 0; font-size: 28px;">Benzochem Industries</h1>
        <p style="color: white; margin: 10px 0 0 0; opacity: 0.9;">Quotation Reminder</p>
    </div>
    
    <div style="background: white; padding: 40px; border: 1px solid #e2e8f0; border-top: none; border-radius: 0 0 10px 10px;">
        <h2 style="color: #1a202c; margin-bottom: 20px;">Hello John Doe,</h2>
        
        <p style="font-size: 16px; margin-bottom: 25px;">
            We sent you a quotation recently and wanted to follow up to see if you have any questions.
        </p>
        
        <div style="background: #f5f3ff; border: 1px solid #ddd6fe; padding: 25px; border-radius: 8px; margin: 25px 0;">
            <h3 style="color: #5b21b6; margin-bottom: 15px;">Important:</h3>
            <p style="margin: 0; color: #5b21b6;">
                Please log in to your account to review the quotation details and let us know how you&apos;d like to proceed.
            </p>
        </div>
        
        <div style="text-align: center; margin: 30px 0;">
            <a href="#" style="background: linear-gradient(135deg, #8b5cf6 0%, #6d28d9 100%); color: white; padding: 12px 30px; text-decoration: none; border-radius: 6px; font-weight: bold;">View Your Quotation</a>
        </div>
        
        <p style="color: #64748b; font-size: 14px; margin-top: 30px;">
            We&apos;re here to help if you need any clarification!
        </p>
    </div>
    
    <div style="text-align: center; padding: 20px; color: #64748b; font-size: 12px;">
        © 2024 Benzochem Industries. All rights reserved.
    </div>
</body>
</html>'></iframe>
                </div>
                <div class="template-actions">
                    <a href="#" class="btn" onclick="openFullPreview('reminder')">Full Preview</a>
                    <a href="#" class="btn btn-secondary" onclick="copyTemplate('reminder')">Copy HTML</a>
                </div>
            </div>

            <!-- Admin Notification Template -->
            <div class="template-card" id="admin-notification">
                <div class="template-header">
                    <div class="template-title">👨‍💼 Admin Notification</div>
                    <div class="template-description">Sent to admin when new quotation request is received</div>
                </div>
                <div class="template-preview">
                    <iframe class="template-iframe" srcdoc='<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>New Quotation Request - Benzochem Industries</title>
</head>
<body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px;">
    <div style="background: linear-gradient(135deg, #dc2626 0%, #991b1b 100%); padding: 30px; text-align: center; border-radius: 10px 10px 0 0;">
        <h1 style="color: white; margin: 0; font-size: 28px;">Benzochem Industries</h1>
        <p style="color: white; margin: 10px 0 0 0; opacity: 0.9;">New Quotation Request</p>
    </div>
    
    <div style="background: white; padding: 40px; border: 1px solid #e2e8f0; border-top: none; border-radius: 0 0 10px 10px;">
        <h2 style="color: #1a202c; margin-bottom: 20px;">Hello Admin,</h2>
        
        <p style="font-size: 16px; margin-bottom: 25px;">
            A new quotation request has been received from <strong>John Doe</strong>.
        </p>
        
        <div style="background: #fef2f2; border: 1px solid #fecaca; padding: 25px; border-radius: 8px; margin: 25px 0;">
            <h3 style="color: #dc2626; margin-bottom: 15px;">Customer Details:</h3>
            <p style="margin: 5px 0;"><strong>Name:</strong> John Doe</p>
            <p style="margin: 5px 0;"><strong>Email:</strong> <EMAIL></p>
            <p style="margin: 5px 0;"><strong>Business:</strong> ABC Chemicals Ltd</p>
            <p style="margin: 5px 0;"><strong>Phone:</strong> +91 98765 43210</p>
            <p style="margin: 5px 0;"><strong>Urgency:</strong> URGENT</p>
        </div>
        
        <div style="background: #f8fafc; padding: 25px; border-radius: 8px; margin: 25px 0;">
            <h3 style="color: #1a202c; margin-bottom: 15px;">Requested Products:</h3>
            <ul style="margin: 0; padding-left: 20px;">
                <li><strong>Sodium Chloride</strong> - 100 kg (Industrial Grade)</li>
                <li><strong>Potassium Hydroxide</strong> - 50 kg (Laboratory Grade)</li>
            </ul>
        </div>
        
        <div style="background: #f0f9ff; border: 1px solid #bae6fd; padding: 25px; border-radius: 8px; margin: 25px 0;">
            <h3 style="color: #0369a1; margin-bottom: 15px;">Additional Requirements:</h3>
            <p style="margin: 0; color: #0369a1;">Need delivery within 2 weeks. Please provide COA for all products.</p>
        </div>
        
        <div style="text-align: center; margin: 30px 0;">
            <a href="#" style="background: linear-gradient(135deg, #dc2626 0%, #991b1b 100%); color: white; padding: 12px 30px; text-decoration: none; border-radius: 6px; font-weight: bold;">Review & Respond</a>
        </div>
        
        <p style="color: #64748b; font-size: 14px; margin-top: 30px;">
            Please log in to the admin dashboard to review and respond to this quotation request.
        </p>
    </div>
    
    <div style="text-align: center; padding: 20px; color: #64748b; font-size: 12px;">
        © 2024 Benzochem Industries. All rights reserved.
    </div>
</body>
</html>'></iframe>
                </div>
                <div class="template-actions">
                    <a href="#" class="btn" onclick="openFullPreview('admin-notification')">Full Preview</a>
                    <a href="#" class="btn btn-secondary" onclick="copyTemplate('admin-notification')">Copy HTML</a>
                </div>
            </div>
        </div>

        <div class="info-section">
            <h2 class="info-title">🛠️ Technical Details</h2>
            <p class="info-text">
                All email templates are generated dynamically using TypeScript functions in the 
                <code>gmail-quotation-service.ts</code> file. They support:
            </p>
            <ul class="template-list">
                <li>📱 Responsive design for mobile and desktop</li>
                <li>🎨 Modern CSS styling with gradients and shadows</li>
                <li>📊 Dynamic GST calculations and pricing breakdown</li>
                <li>🔗 Dynamic links to user dashboard and admin panel</li>
                <li>📧 Gmail API integration for reliable delivery</li>
                <li>🌐 Environment-based URL configuration</li>
            </ul>
        </div>
    </div>

    <script>
        function openFullPreview(templateType) {
            // Create a new window with the full template
            const newWindow = window.open('', '_blank', 'width=800,height=600,scrollbars=yes');
            const iframe = document.querySelector(`#${templateType} .template-iframe`);
            newWindow.document.write(iframe.srcdoc);
            newWindow.document.close();
        }

        function copyTemplate(templateType) {
            const iframe = document.querySelector(`#${templateType} .template-iframe`);
            const htmlContent = iframe.srcdoc;
            
            navigator.clipboard.writeText(htmlContent).then(() => {
                alert('Template HTML copied to clipboard!');
            }).catch(err => {
                console.error('Failed to copy: ', err);
                // Fallback for older browsers
                const textArea = document.createElement('textarea');
                textArea.value = htmlContent;
                document.body.appendChild(textArea);
                textArea.select();
                document.execCommand('copy');
                document.body.removeChild(textArea);
                alert('Template HTML copied to clipboard!');
            });
        }

        // Smooth scrolling for navigation links
        document.querySelectorAll('.nav-link').forEach(link => {
            link.addEventListener('click', function(e) {
                e.preventDefault();
                const targetId = this.getAttribute('href').substring(1);
                const targetElement = document.getElementById(targetId);
                if (targetElement) {
                    targetElement.scrollIntoView({ behavior: 'smooth' });
                }
            });
        });
    </script>
</body>
</html>